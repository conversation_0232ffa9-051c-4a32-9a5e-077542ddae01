'use client'

import React, { useState } from 'react'
import { But<PERSON> } from './button'
import { Card, CardContent, CardHeader, CardTitle } from './card'
import { Badge } from './badge'

interface DatabaseSetupBannerProps {
  dbError: string | null
  className?: string
}

export function DatabaseSetupBanner({ dbError, className = '' }: DatabaseSetupBannerProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  if (!dbError) return null

  const isSetupError = dbError.includes('Database not set up') || 
                      dbError.includes('relation "profiles" does not exist')

  if (!isSetupError) {
    // Show generic error for other database issues
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 mb-4 ${className}`}>
        <div className="flex items-center space-x-2">
          <span className="text-red-500">⚠️</span>
          <span className="text-red-800 font-medium">Database Error</span>
        </div>
        <p className="text-red-700 text-sm mt-1">{dbError}</p>
      </div>
    )
  }

  return (
    <Card className={`border-amber-200 bg-amber-50 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-amber-600">🔧</span>
            <CardTitle className="text-amber-800">Database Setup Required</CardTitle>
            <Badge variant="secondary" className="bg-amber-100 text-amber-800">
              Setup Pending
            </Badge>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-amber-700 hover:text-amber-800"
          >
            {isExpanded ? '▼' : '▶'} Setup Guide
          </Button>
        </div>
      </CardHeader>
      
      {isExpanded && (
        <CardContent className="pt-0">
          <div className="space-y-4">
                         <p className="text-amber-800 text-sm">
               The Mouvers database hasn&apos;t been set up yet. Follow these steps to complete the setup:
             </p>
            
            <div className="bg-white rounded-lg p-4 border border-amber-200">
              <h4 className="font-medium text-amber-900 mb-3">Setup Instructions:</h4>
              <ol className="list-decimal list-inside space-y-2 text-sm text-amber-800">
                <li>
                  Open your <strong>Supabase Dashboard</strong> at{' '}
                  <a 
                    href="https://supabase.com/dashboard" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="underline hover:text-amber-900"
                  >
                    supabase.com/dashboard
                  </a>
                </li>
                <li>Select your <strong>Mouvers project</strong> (yhiffsfdiqnjdqmnlghp)</li>
                <li>Go to <strong>SQL Editor</strong> in the left sidebar</li>
                <li>Create a <strong>New Query</strong></li>
                <li>Copy and paste the contents of <code className="bg-amber-100 px-1 rounded">database-setup.sql</code></li>
                <li>Click <strong>Run</strong> to execute the setup script</li>
                <li>Refresh this page once complete</li>
              </ol>
            </div>

            <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
              <h4 className="font-medium text-blue-900 mb-2">📁 Find the setup file:</h4>
              <p className="text-blue-800 text-sm">
                The <code className="bg-blue-100 px-1 rounded">database-setup.sql</code> file is located 
                in your project root directory. It contains all the necessary tables, policies, and functions.
              </p>
            </div>

            <div className="flex space-x-3">
              <Button
                onClick={() => window.location.reload()}
                className="bg-amber-600 hover:bg-amber-700 text-white"
              >
                Refresh Page
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsExpanded(false)}
                className="border-amber-300 text-amber-700 hover:bg-amber-100"
              >
                Hide Guide
              </Button>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  )
}

interface SetupProgressProps {
  step: number
  totalSteps: number
  currentStepName: string
  className?: string
}

export function SetupProgress({ 
  step, 
  totalSteps, 
  currentStepName, 
  className = '' 
}: SetupProgressProps) {
  const percentage = (step / totalSteps) * 100

  return (
    <div className={`bg-white rounded-lg p-4 border ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-gray-900">Setup Progress</span>
        <span className="text-sm text-gray-600">{step}/{totalSteps}</span>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
        <div 
          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
          style={{ width: `${percentage}%` }}
        />
      </div>
      
      <p className="text-sm text-gray-600">{currentStepName}</p>
    </div>
  )
} 