'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { LogoutButton } from '@/components/auth/logout-button'
import { Package, Truck, DollarSign, BarChart3, Settings, CreditCard, MapPin, Mail } from 'lucide-react'

export default function AdminDashboard() {
  const { user, profile, loading, isAdmin } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/')
      return
    }

    if (!loading && profile && !isAdmin) {
      router.push('/dashboard')
      return
    }
  }, [user, profile, loading, router, isAdmin])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    )
  }

  if (!user || !profile || !isAdmin) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-xl font-bold text-black">Mouvers Admin</h1>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                {profile.full_name || user.email}
              </span>
              <Badge variant="default">Administrador</Badge>
              <LogoutButton className="text-sm border border-black rounded-[10px]">
                Cerrar Sesión
              </LogoutButton>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Welcome Section */}
          <div 
            className="mb-8 bg-cover bg-center bg-no-repeat rounded-lg p-8 min-h-[200px] flex items-center justify-end"
            style={{ backgroundImage: 'url(/back.png)' }}
          >
            <div className="text-right bg-black/50 p-6 rounded-lg backdrop-blur-sm">
              <h2 className="text-2xl font-bold text-white mb-2">
                Panel de Administración
              </h2>
              <p className="text-gray-200">
                Gestiona todos los pedidos, usuarios y operaciones de Mouvers
              </p>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card 
              className="bg-cover bg-center bg-no-repeat relative overflow-hidden"
              style={{ backgroundImage: 'url(/total.jpg)' }}
            >
              <div className="absolute inset-0 bg-black/50"></div>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
                <CardTitle className="text-sm font-medium text-white">Pedidos Totales</CardTitle>
                <Badge variant="secondary">0</Badge>
              </CardHeader>
              <CardContent className="relative z-10">
                <div className="text-2xl font-bold text-white">0</div>
                <p className="text-xs text-gray-200">Todos los pedidos</p>
              </CardContent>
            </Card>

            <Card 
              className="bg-cover bg-center bg-no-repeat relative overflow-hidden"
              style={{ backgroundImage: 'url(/order.jpg)' }}
            >
              <div className="absolute inset-0 bg-black/50"></div>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 relative z-10">
                <CardTitle className="text-sm font-medium text-white">Pedidos Activos</CardTitle>
                <Badge variant="secondary">0</Badge>
              </CardHeader>
              <CardContent className="relative z-10">
                <div className="text-2xl font-bold text-white">0</div>
                <p className="text-xs text-gray-200">En proceso</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Usuarios</CardTitle>
                <Badge variant="secondary">0</Badge>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0</div>
                <p className="text-xs text-gray-600">Clientes registrados</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Ingresos</CardTitle>
                <Badge variant="secondary">$0</Badge>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$0.00</div>
                <p className="text-xs text-gray-600">Este mes</p>
              </CardContent>
            </Card>
          </div>

          {/* Management Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardHeader>
                <CardTitle>Gestión de Usuarios</CardTitle>
                <CardDescription>
                  Administra usuarios y sus perfiles
                </CardDescription>
              </CardHeader>
              <CardContent className="grid grid-cols-1 gap-3">
                <Link href="/admin/users">
                  <Button className="w-full cursor-pointer">
                    Ver Usuarios
                  </Button>
                </Link>
                <Link href="/admin/users">
                  <Button variant="outline" className="w-full cursor-pointer">
                    Usuarios Activos
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Gestión de Pedidos</CardTitle>
                <CardDescription>
                  Ve y gestiona todos los pedidos del sistema
                </CardDescription>
              </CardHeader>
              <CardContent className="grid grid-cols-1 gap-3">
                <Link href="/admin/orders">
                  <Button className="w-full cursor-pointer">
                    Ver Todos los Pedidos
                  </Button>
                </Link>
                <Link href="/admin/orders">
                  <Button variant="outline" className="w-full cursor-pointer">
                    Pedidos Pendientes
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Reportes</CardTitle>
                <CardDescription>
                  Analiza el rendimiento y estadísticas
                </CardDescription>
              </CardHeader>
              <CardContent className="grid grid-cols-1 gap-3">
                <Button className="w-full cursor-pointer">
                  Reporte de Ventas
                </Button>
                <Button variant="outline" className="w-full cursor-pointer">
                  Métricas de Entrega
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <Card>
              <CardHeader>
                <CardTitle>Acciones Rápidas</CardTitle>
                <CardDescription>
                  Operaciones frecuentes del administrador
                </CardDescription>
              </CardHeader>
              <CardContent className="grid grid-cols-1 gap-3">
                <Button variant="outline" className="w-full justify-start cursor-pointer">
                  <Package className="w-4 h-4 mr-2" />
                  Actualizar Estado de Pedido
                </Button>
                <Button variant="outline" className="w-full justify-start cursor-pointer">
                  <Truck className="w-4 h-4 mr-2" />
                  Asignar Conductor
                </Button>
                <Button variant="outline" className="w-full justify-start cursor-pointer">
                  <DollarSign className="w-4 h-4 mr-2" />
                  Procesar Pagos
                </Button>
                <Button variant="outline" className="w-full justify-start cursor-pointer">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Exportar Datos
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Configuración</CardTitle>
                <CardDescription>
                  Ajustes del sistema y configuración
                </CardDescription>
              </CardHeader>
              <CardContent className="grid grid-cols-1 gap-3">
                <Button variant="outline" className="w-full justify-start cursor-pointer">
                  <Settings className="w-4 h-4 mr-2" />
                  Configuración General
                </Button>
                <Button variant="outline" className="w-full justify-start cursor-pointer">
                  <CreditCard className="w-4 h-4 mr-2" />
                  Configurar Pagos
                </Button>
                <Button variant="outline" className="w-full justify-start cursor-pointer">
                  <MapPin className="w-4 h-4 mr-2" />
                  Zonas de Entrega
                </Button>
                <Button variant="outline" className="w-full justify-start cursor-pointer">
                  <Mail className="w-4 h-4 mr-2" />
                  Notificaciones
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Actividad Reciente del Sistema</CardTitle>
              <CardDescription>
                Últimas acciones y eventos importantes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <p>No hay actividad reciente</p>
                <p className="text-sm">La actividad del sistema aparecerá aquí</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
} 