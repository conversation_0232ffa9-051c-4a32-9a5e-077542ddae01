'use client'

import { useState, useEffect } from 'react'

import { useAuth } from '@/hooks/useAuth'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { OrdersManagement } from '@/components/admin/orders-management'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { createClient } from '@/utils/supabase/client'

interface Order {
  id: string
  customer_id: string
  customer_name: string
  customer_email: string
  status: 'pending' | 'confirmed' | 'in-transit' | 'delivered' | 'cancelled'
  pickup_address: {
    street_address: string
    city: string
    state: string
    contact_name: string
    contact_phone: string
  }
  delivery_addresses: {
    street_address: string
    city: string
    state: string
    recipient_name: string
    phone: string
  }[]
  package_details: {
    description: string
    weight: string
    dimensions: string
    value: string
  }
  total_cost: number
  payment_status: 'pending' | 'paid' | 'failed'
  created_at: string
  updated_at: string
}

function AdminOrdersContent() {
  const { user, isAdmin } = useAuth()
  const [orders, setOrders] = useState<Order[]>([])
  const [ordersLoading, setOrdersLoading] = useState(true)

  const supabase = createClient()

  useEffect(() => {
    if (user && isAdmin) {
      fetchOrders()
    }
  }, [user, isAdmin])

  const fetchOrders = async () => {
    try {
      // Fetch orders with customer information
      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          profiles!customer_id (
            full_name,
            email
          )
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching orders:', error)
        return
      }

      // Transform data to include customer info
      const transformedOrders = data?.map(order => ({
        ...order,
        customer_name: order.profiles?.full_name || 'Unknown',
        customer_email: order.profiles?.email || 'Unknown'
      })) || []

      setOrders(transformedOrders)
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setOrdersLoading(false)
    }
  }

  const handleUpdateOrderStatus = async (orderId: string, newStatus: Order['status']) => {
    try {
      const { error } = await supabase
        .from('orders')
        .update({ status: newStatus })
        .eq('id', orderId)

      if (error) {
        console.error('Error updating order status:', error)
        alert('Failed to update order status')
        return
      }

      // Update local state
      setOrders(prev => prev.map(order => 
        order.id === orderId 
          ? { ...order, status: newStatus }
          : order
      ))
    } catch (error) {
      console.error('Error:', error)
      alert('Failed to update order status')
    }
  }



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                onClick={() => window.history.back()}
                className="text-blue-600"
              >
                ← Volver al Panel
              </Button>
              <h1 className="text-xl font-bold text-black">Gestión de Pedidos</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                {user?.email || 'Admin'}
              </span>
              <Badge variant="default">Admin</Badge>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <OrdersManagement 
            orders={orders} 
            onUpdateOrderStatus={handleUpdateOrderStatus}
            loading={ordersLoading} 
          />
        </div>
      </main>
    </div>
  )
}

export default function AdminOrdersPage() {
  return (
    <ProtectedRoute requireAdmin>
      <AdminOrdersContent />
    </ProtectedRoute>
  )
} 