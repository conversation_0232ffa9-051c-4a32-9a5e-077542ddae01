/**
 * Test script to validate the password reset flow with PKCE
 * This tests the complete flow from email request to password confirmation
 */

const { createClient } = require('@supabase/supabase-js')

// Configuration - replace with your actual values
const SUPABASE_URL = 'https://yhiffsfdiqnjdqmnlghp.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InloaWZmc2ZkaXFuamRxbW5sZ2hwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4ODU4NzIsImV4cCI6MjA2NjQ2MTg3Mn0.a6WnBiKj9fCvPtsxXpG8PnniKRr2wX82tkL8TwOj1jA'
const TEST_EMAIL = '<EMAIL>' // Replace with a test email
const API_BASE_URL = 'http://localhost:3000'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    flowType: 'pkce',
    persistSession: false,
    detectSessionInUrl: false,
    autoRefreshToken: false,
  },
})

async function testPasswordResetRequest() {
  console.log('\n🔄 Testing password reset request...')
  
  try {
    const { data, error } = await supabase.auth.resetPasswordForEmail(
      TEST_EMAIL,
      {
        redirectTo: `${API_BASE_URL}/reset-password/confirm`,
      }
    )
    
    if (error) {
      console.error('❌ Password reset request failed:', error.message)
      return false
    }
    
    console.log('✅ Password reset request successful!')
    console.log('📧 Check your email for the reset link')
    console.log('🔗 The link should redirect to:', `${API_BASE_URL}/reset-password/confirm`)
    
    return true
  } catch (error) {
    console.error('❌ Unexpected error:', error.message)
    return false
  }
}

async function testPasswordResetConfirmation(tokenHash) {
  console.log('\n🔄 Testing password reset confirmation...')
  
  const testPassword = 'NewSecurePassword123!'
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/auth/reset-password/confirm`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        password: testPassword,
        confirmPassword: testPassword,
        code: tokenHash
      }),
    })
    
    const data = await response.json()
    
    if (!response.ok) {
      console.error('❌ Password reset confirmation failed:', data.error)
      return false
    }
    
    console.log('✅ Password reset confirmation successful!')
    console.log('🔐 Password has been updated')
    
    return true
  } catch (error) {
    console.error('❌ Unexpected error:', error.message)
    return false
  }
}

async function main() {
  console.log('🧪 Password Reset Flow Test')
  console.log('============================')
  console.log(`📧 Test email: ${TEST_EMAIL}`)
  console.log(`🌐 API base URL: ${API_BASE_URL}`)
  
  // Step 1: Request password reset
  const requestSuccess = await testPasswordResetRequest()
  
  if (!requestSuccess) {
    console.log('\n❌ Test failed at password reset request step')
    process.exit(1)
  }
  
  console.log('\n📋 Next steps:')
  console.log('1. Check your email for the password reset link')
  console.log('2. Extract the token_hash parameter from the URL')
  console.log('3. Run: node test-password-reset-flow.js confirm <token_hash>')
  console.log('\nExample:')
  console.log('node test-password-reset-flow.js confirm abc123def456...')
}

// Handle command line arguments
const args = process.argv.slice(2)

if (args[0] === 'confirm' && args[1]) {
  // Test confirmation with provided token hash
  testPasswordResetConfirmation(args[1])
    .then(success => {
      if (success) {
        console.log('\n✅ Password reset flow test completed successfully!')
      } else {
        console.log('\n❌ Password reset confirmation test failed')
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('\n❌ Test error:', error.message)
      process.exit(1)
    })
} else {
  // Run the main test
  main().catch(error => {
    console.error('\n❌ Test error:', error.message)
    process.exit(1)
  })
}
