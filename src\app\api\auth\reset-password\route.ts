import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'
import { ratelimit, rateLimitConfigs, getClientIP, formatRateLimitError } from '@/lib/rate-limit'
import { auditLogger } from '@/lib/audit-logger'
import { customerPasswordResetService } from '@/lib/customer-password-reset'

// Rate limiting configuration
const RATE_LIMIT_REQUESTS = 3 // Max 3 requests
const RATE_LIMIT_WINDOW = 15 * 60 * 1000 // 15 minutes in milliseconds
const RATE_LIMIT_GLOBAL_REQUESTS = 10 // Max 10 requests globally
const RATE_LIMIT_GLOBAL_WINDOW = 60 * 1000 // 1 minute in milliseconds

// In-memory rate limiting store (in production, use Redis or database)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()
const globalRateLimitStore = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(identifier: string, maxRequests: number, windowMs: number, store: Map<string, { count: number; resetTime: number }>): { allowed: boolean; resetTime?: number } {
  const now = Date.now()
  const key = identifier
  const record = store.get(key)

  if (!record || now > record.resetTime) {
    // First request or window expired
    store.set(key, { count: 1, resetTime: now + windowMs })
    return { allowed: true }
  }

  if (record.count >= maxRequests) {
    // Rate limit exceeded
    return { allowed: false, resetTime: record.resetTime }
  }

  // Increment count
  record.count++
  store.set(key, record)
  return { allowed: true }
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email) && email.length <= 254
}

export async function POST(request: NextRequest) {
  const clientIP = getClientIP(request)
  const userAgent = request.headers.get('user-agent') || 'unknown'

  try {
    const { email } = await request.json()

    // Input validation
    if (!email || typeof email !== 'string') {
      await auditLogger.logInvalidResetAttempt({
        email: email || 'invalid',
        ip_address: clientIP,
        user_agent: userAgent,
        reason: 'Invalid email format or missing email'
      })

      return NextResponse.json(
        { error: 'Email is required and must be a string' },
        { status: 400 }
      )
    }

    if (!isValidEmail(email)) {
      await auditLogger.logInvalidResetAttempt({
        email,
        ip_address: clientIP,
        user_agent: userAgent,
        reason: 'Invalid email format'
      })

      return NextResponse.json(
        { error: 'Please provide a valid email address' },
        { status: 400 }
      )
    }
    
    // Use the new rate limiting utility
    const globalRateCheck = ratelimit.check('global', rateLimitConfigs.globalApi)
    if (!globalRateCheck.allowed) {
      await auditLogger.logRateLimitExceeded({
        email,
        ip_address: clientIP,
        user_agent: userAgent,
        limit_type: 'global',
        reset_time: globalRateCheck.resetTime
      })

      const resetTimeMinutes = Math.ceil((globalRateCheck.resetTime! - Date.now()) / 60000)
      return NextResponse.json(
        {
          error: 'Too many password reset requests. Please try again later.',
          retryAfter: resetTimeMinutes
        },
        { status: 429 }
      )
    }

    // Check per-IP rate limit
    const ipRateCheck = ratelimit.check(clientIP, rateLimitConfigs.passwordReset)
    if (!ipRateCheck.allowed) {
      await auditLogger.logRateLimitExceeded({
        email,
        ip_address: clientIP,
        user_agent: userAgent,
        limit_type: 'ip',
        reset_time: ipRateCheck.resetTime
      })

      const resetTimeMinutes = Math.ceil((ipRateCheck.resetTime! - Date.now()) / 60000)
      return NextResponse.json(
        {
          error: 'Too many password reset attempts from this location. Please try again later.',
          retryAfter: resetTimeMinutes
        },
        { status: 429 }
      )
    }

    // Check per-email rate limit (prevent targeting specific users)
    const emailRateCheck = ratelimit.check(`email:${email.toLowerCase()}`, rateLimitConfigs.passwordReset)
    if (!emailRateCheck.allowed) {
      await auditLogger.logRateLimitExceeded({
        email,
        ip_address: clientIP,
        user_agent: userAgent,
        limit_type: 'email',
        reset_time: emailRateCheck.resetTime
      })

      const resetTimeMinutes = Math.ceil((emailRateCheck.resetTime! - Date.now()) / 60000)
      return NextResponse.json(
        {
          error: 'Too many password reset requests for this email. Please try again later.',
          retryAfter: resetTimeMinutes
        },
        { status: 429 }
      )
    }

    // Initialize Supabase client
    const supabase = await createClient()

    // Get comprehensive customer data for enhanced password reset
    const customerData = await customerPasswordResetService.getCustomerResetData(email)

    // For security, we'll always return success even if user doesn't exist
    // This prevents email enumeration attacks

    if (customerData) {
      // User exists, proceed with password reset
      const baseUrl = process.env.NEXT_PUBLIC_VERCEL_URL
        ? `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`
        : process.env.NEXT_PUBLIC_APP_URL
        ? process.env.NEXT_PUBLIC_APP_URL
        : 'http://localhost:3000'

      const redirectUrl = `${baseUrl}/reset-password/confirm`

      const { error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: redirectUrl,
      })

      if (resetError) {
        console.error('Password reset error:', resetError)

        // Log the failed attempt with customer-specific data
        await customerPasswordResetService.logCustomerPasswordReset(customerData, {
          ip_address: clientIP,
          user_agent: userAgent,
          success: false,
          error_message: resetError.message
        })

        // Don't expose internal errors to client
        return NextResponse.json(
          { error: 'Unable to process password reset request. Please try again later.' },
          { status: 500 }
        )
      }

      // Send customer-specific notification
      await customerPasswordResetService.sendCustomerResetNotification({
        email: customerData.email,
        full_name: customerData.full_name,
        preferred_name: customerData.customer_profile?.preferred_name,
        emergency_contact_name: customerData.customer_profile?.emergency_contact_name,
        emergency_contact_phone: customerData.customer_profile?.emergency_contact_phone,
        customer_tier: customerData.customer_profile?.customer_tier,
        loyalty_points: customerData.customer_profile?.loyalty_points,
        reset_timestamp: new Date().toISOString(),
        ip_address: clientIP,
        user_agent: userAgent
      })

      // Notify emergency contact for high-value accounts
      if (customerPasswordResetService.shouldApplyEnhancedSecurity(customerData)) {
        await customerPasswordResetService.notifyEmergencyContact(customerData, {
          ip_address: clientIP,
          user_agent: userAgent,
          timestamp: new Date().toISOString()
        })
      }

      // Log successful password reset request with customer data
      await customerPasswordResetService.logCustomerPasswordReset(customerData, {
        ip_address: clientIP,
        user_agent: userAgent,
        success: true
      })
    } else {
      // User doesn't exist, but we still return success for security
      // Log the attempt for monitoring
      await auditLogger.logPasswordResetRequest({
        email,
        ip_address: clientIP,
        user_agent: userAgent,
        success: false,
        error_message: 'Email not found'
      })
    }

    // Always return success to prevent email enumeration
    return NextResponse.json({
      success: true,
      message: 'If an account with that email exists, you will receive a password reset link shortly.'
    })

  } catch (error) {
    console.error('Password reset API error:', error)
    return NextResponse.json(
      { error: 'Internal server error. Please try again later.' },
      { status: 500 }
    )
  }
}

// Cleanup function to remove expired rate limit entries
setInterval(() => {
  const now = Date.now()
  
  // Clean up IP-based rate limits
  for (const [key, record] of rateLimitStore.entries()) {
    if (now > record.resetTime) {
      rateLimitStore.delete(key)
    }
  }
  
  // Clean up global rate limits
  for (const [key, record] of globalRateLimitStore.entries()) {
    if (now > record.resetTime) {
      globalRateLimitStore.delete(key)
    }
  }
}, 5 * 60 * 1000) // Clean up every 5 minutes
