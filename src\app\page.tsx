'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { LoginForm } from '@/components/auth/login-form'
import { AuthLayout } from '@/components/auth/auth-layout'
import { DatabaseSetupBanner } from '@/components/ui/database-setup-banner'

export default function HomePage() {
  const { user, loading, dbError } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && user && !dbError) {
      router.push('/dashboard')
    }
  }, [user, loading, router, dbError])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
      </div>
    )
  }

  if (user && !dbError) {
    return null // Will redirect to dashboard
  }

  return (
    <AuthLayout>
      <div className="w-full max-w-md">
        {/* Database Setup Banner */}
        <DatabaseSetupBanner dbError={dbError} className="mb-6" />
        
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            Mouvers
          </h1>
          <p className="text-gray-300">
            Accede a tu cuenta para gestionar entregas
          </p>
        </div>
        
        <LoginForm />
      </div>
    </AuthLayout>
  )
}
