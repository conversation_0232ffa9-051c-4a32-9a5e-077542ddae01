'use client'

import { create<PERSON>ontext, useContext, useEffect, useState, ReactNode, useCallback } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { createClient } from '@/utils/supabase/client'
import { useRouter } from 'next/navigation'

interface UserProfile {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  role: 'customer' | 'admin'
  created_at: string
  updated_at: string
}

interface AuthContextType {
  user: User | null
  session: Session | null
  profile: UserProfile | null
  loading: boolean
  dbError: string | null
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  signUp: (email: string, password: string, metadata?: Record<string, string>) => Promise<{ data: any; error: any }>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  signOut: () => Promise<{ error: any }>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  resetPassword: (email: string) => Promise<{ data: any; error: any }>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  updatePassword: (password: string, additionalMetadata?: Record<string, any>) => Promise<{ data: any; error: any }>
  isAdmin: boolean
  isCustomer: boolean
  fetchProfile: (userId: string) => Promise<UserProfile | null>
  refreshAuth: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [dbError, setDbError] = useState<string | null>(null)
  const [isInPasswordReset, setIsInPasswordReset] = useState(false)
  const router = useRouter()

  const supabase = createClient()

  // Check if we're in a password reset flow
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search)
      const code = urlParams.get('code')
      
      if (code) {
        setIsInPasswordReset(true)
      } else {
        setIsInPasswordReset(false)
      }
    }
  }, [])

  const fetchProfile = useCallback(async (userId: string): Promise<UserProfile | null> => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        if (error.code === 'PGRST116' || error.message?.includes('relation "profiles" does not exist')) {
          setDbError('Database not set up. Please run the database setup script.')
          if (process.env.NODE_ENV === 'development') {
            console.warn('Database setup required. Please execute database-setup.sql in Supabase.')
          }
          return null
        }

        if (process.env.NODE_ENV === 'development') {
          console.error('Error fetching profile:', error)
        }
        setDbError(`Profile fetch error: ${error.message}`)
        return null
      }

      setDbError(null)
      return data as UserProfile
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching profile:', error)
      }
      setDbError('Failed to connect to database')
      return null
    }
  }, [supabase])

  const refreshAuth = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      setSession(session)
      setUser(session?.user ?? null)
      
      if (session?.user) {
        const userProfile = await fetchProfile(session.user.id)
        setProfile(userProfile)
      } else {
        setProfile(null)
      }
    } catch (err) {
      const error = err as Error

      // Handle invalid or missing refresh token by signing out
      if (error.message.includes('Invalid Refresh Token')) {
        await supabase.auth.signOut()
        setUser(null)
        setSession(null)
        setProfile(null)
        // Redirect to login or home can be handled elsewhere
      }

      if (process.env.NODE_ENV === 'development') {
        console.error('Error refreshing auth:', error)
      }
      setDbError('Failed to refresh authentication')
    }
  }

  // Initialize auth state
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    let mounted = true
    let timeoutId: NodeJS.Timeout

    const getInitialSession = async () => {

      
      try {
        // Add timeout to prevent infinite loading
        const sessionPromise = supabase.auth.getSession()
        const timeoutPromise = new Promise((_, reject) => {
          timeoutId = setTimeout(() => reject(new Error('Session timeout')), 5000) // 5 seconds max
        })

        const result = await Promise.race([sessionPromise, timeoutPromise])
        const { data: { session } } = result as { data: { session: Session | null } }
        clearTimeout(timeoutId)
        

        
        if (!mounted) return

        setSession(session)
        setUser(session?.user ?? null)
        
        // Fetch profile in background (non-blocking)
        if (session?.user) {

          fetchProfile(session.user.id)
            .then(userProfile => {
              if (mounted) {
                setProfile(userProfile)
              }
            })
            .catch(profileError => {
              if (process.env.NODE_ENV === 'development') {
                console.error('[AuthProvider] Profile fetch failed:', profileError)
              }
              if (mounted) {
                setProfile(null)
                setDbError('Failed to load user profile')
              }
            })
        } else {
          // No session, clear profile
          setProfile(null)
        }
        
        // Set loading to false after session is restored (regardless of profile)
        setLoading(false)
        
        // If we're in password reset flow, don't redirect even if user exists
        if (session?.user && isInPasswordReset) {
          // Don't redirect - let the reset form handle it
        }
      } catch (error) {
        clearTimeout(timeoutId)
        if (mounted) {
          if (process.env.NODE_ENV === 'development') {
            console.error('[AuthProvider] Error getting session:', error)
          }
          
          // If it's a timeout, try to continue without blocking the app
          if (error instanceof Error && error.message.includes('Session timeout')) {

            setSession(null)
            setUser(null)
            setProfile(null)
            setDbError(null) // Don't show error for timeout
          } else {
            setDbError('Failed to initialize authentication')
          }
          
          // Force set loading to false even on error
          setLoading(false)
        }
      } finally {

      }
    }

    getInitialSession()

    return () => {
      mounted = false
      if (timeoutId) clearTimeout(timeoutId)
    }
  }, [fetchProfile, supabase])

  // Listen for auth changes
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event: string, session: Session | null) => {

        
        setSession(session)
        setUser(session?.user ?? null)
        
        // Set loading to false immediately
        setLoading(false)
        
        if (session?.user) {
          // Fetch profile in background (non-blocking)
          fetchProfile(session.user.id)
            .then(userProfile => {
              setProfile(userProfile)
            })
            .catch(profileError => {
              if (process.env.NODE_ENV === 'development') {
                console.error('[AuthProvider] Profile fetch failed in auth state change:', profileError)
              }
              setProfile(null)
              setDbError('Failed to load user profile')
            })
        } else {
          setProfile(null)
          setDbError(null)
        }
        
        // If we're in password reset flow, don't redirect even if user exists
        if (session?.user && isInPasswordReset) {
          // Don't redirect - let the reset form handle it
        }

        // Handle navigation based on auth events
        if (event === 'SIGNED_OUT') {
          router.push('/')
        } else if (event === 'PASSWORD_RECOVERY') {
          // User has successfully reset their password
          // Don't redirect automatically - let the reset form handle it
          // The user should complete the password reset flow
        } else if (event === 'SIGNED_IN' && isInPasswordReset) {
          // Don't redirect if we're in password reset flow
          // Let the reset form handle the flow
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [router, supabase, fetchProfile])

  const signIn = async (email: string, password: string) => {
    setLoading(true)
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { data, error }
  }

  const signUp = async (email: string, password: string, metadata?: Record<string, string>) => {
    setLoading(true)
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata,
      },
    })
    return { data, error }
  }

  const signOut = async () => {
    try {
      setLoading(true)
      const { error } = await supabase.auth.signOut()
      if (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error during sign-out:', error)
        }
        return { error }
      }
      return { error: null }
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Unexpected error during sign-out:', err)
      }
      return { error: err as Error }
    } finally {
      setLoading(false)
    }
  }

  const resetPassword = async (email: string) => {
    // Determine the correct base URL for the password-reset redirect.
    // 1. If we are running in the browser, rely on the current origin so that whatever
    //    domain (localhost, staging, production) the user is on is automatically used.
    // 2. If we are on the server (should be rare here because this file is "use client"),
    //    fall back to Vercel-provided URL first, then to an explicitly configured
    //    NEXT_PUBLIC_APP_URL, and finally to localhost.

    const baseUrl = typeof window !== 'undefined'
      ? window.location.origin
      : process.env.NEXT_PUBLIC_VERCEL_URL
      ? `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`
      : process.env.NEXT_PUBLIC_APP_URL
      ? process.env.NEXT_PUBLIC_APP_URL
      : 'http://localhost:3000'
    
    const redirectUrl = `${baseUrl}/reset-password/confirm`
    
    // Debug: Log the redirect URL
    if (process.env.NODE_ENV === 'development') {
      console.log('Password reset redirect URL:', redirectUrl)
    }
    
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: redirectUrl,
    })
    
    // Debug: Log the response
    if (process.env.NODE_ENV === 'development') {
      console.log('Reset password response:', { data, error })
    }
    
    return { data, error }
  }

  const updatePassword = async (password: string, additionalMetadata?: Record<string, any>) => {
    // Using latest JWT-based password update with enhanced context (July 2025)
    const { data: sessionData } = await supabase.auth.getSession()

    const { data, error } = await supabase.auth.updateUser({
      password,
      // Enhanced JWT validation for secure password updates with metadata
      data: {
        password_reset_at: new Date().toISOString(),
        password_reset_method: 'user_initiated',
        // Include JWT metadata for enhanced security tracking
        password_reset_aal: sessionData.session?.user?.aal || 'aal1',
        password_reset_session_id: sessionData.session?.user?.session_id,
        password_reset_token_expires_at: sessionData.session?.expires_at,
        // Include any additional metadata passed from the caller
        ...additionalMetadata
      }
    })
    return { data, error }
  }

  const isAdmin = profile?.role === 'admin'
  const isCustomer = profile?.role === 'customer'

  const value: AuthContextType = {
    user,
    session,
    profile,
    loading,
    dbError,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    isAdmin,
    isCustomer,
    fetchProfile,
    refreshAuth,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
} 