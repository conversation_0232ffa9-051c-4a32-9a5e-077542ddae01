'use client'

import { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Users, ShoppingCart, Settings, X, Wallet, CreditCard, Calendar, Mail, Phone, User } from 'lucide-react'
import { createClient } from '@/utils/supabase/client'

interface User {
  id: string
  email: string
  full_name: string
  phone?: string
  role: 'customer' | 'admin'
  created_at: string
  updated_at: string
  orders_count?: number
  total_spent?: number
  last_order?: string
}

interface UserDetails extends User {
  wallet_balance?: number
  customer_profile?: {
    preferred_name?: string
    emergency_contact_name?: string
    emergency_contact_phone?: string
    preferred_delivery_time?: string
    marketing_opt_in?: boolean
    customer_tier?: string
    loyalty_points?: number
    total_orders?: number
    total_spent?: number
  }
  recent_transactions?: Array<{
    id: string
    type: 'deposit' | 'withdrawal' | 'charge'
    amount: number
    description: string
    status: string
    created_at: string
  }>
  recent_orders?: Array<{
    id: string
    pickup_address: string
    delivery_address: string
    total_cost: number
    status: string
    created_at: string
  }>
}

interface UsersManagementProps {
  users: User[]
  onCreateUser?: (userData: Partial<User>) => void
  loading?: boolean
}

export function UsersManagement({ users, onCreateUser, loading }: UsersManagementProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('all')
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [selectedUser, setSelectedUser] = useState<UserDetails | null>(null)
  const [loadingDetails, setLoadingDetails] = useState(false)
  const [newUser, setNewUser] = useState({
    email: '',
    full_name: '',
    phone: '',
    role: 'customer' as 'customer' | 'admin'
  })

  const supabase = createClient()

  const filteredUsers = users.filter(user => {
    const matchesSearch = 
      user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.id.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesRole = roleFilter === 'all' || user.role === roleFilter

    return matchesSearch && matchesRole
  })

  const handleCreateUser = (e: React.FormEvent) => {
    e.preventDefault()
    if (onCreateUser) {
      onCreateUser(newUser)
      setNewUser({ email: '', full_name: '', phone: '', role: 'customer' })
      setShowCreateForm(false)
    }
  }

  const handleViewDetails = async (user: User) => {
    setLoadingDetails(true)
    try {
      const userDetails: UserDetails = { ...user }

      // If user is a customer, fetch wallet, transaction, and customer profile data
      if (user.role === 'customer') {
        // Get wallet balance
        const { data: walletData } = await supabase
          .from('user_wallets')
          .select('balance')
          .eq('user_id', user.id)
          .single()

        if (walletData) {
          userDetails.wallet_balance = parseFloat(walletData.balance)
        }

        // Get customer profile data
        const { data: customerProfileData } = await supabase
          .from('customer_profiles')
          .select('*')
          .eq('user_id', user.id)
          .single()

        if (customerProfileData) {
          userDetails.customer_profile = customerProfileData
        }

        // Get recent transactions
        const { data: transactionsData } = await supabase
          .from('wallet_transactions')
          .select('id, type, amount, description, status, created_at')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(5)

        if (transactionsData) {
          userDetails.recent_transactions = transactionsData
        }

        // Get recent orders
        const { data: ordersData } = await supabase
          .from('orders')
          .select('id, pickup_address, delivery_address, total_cost, status, created_at')
          .eq('customer_id', user.id)
          .order('created_at', { ascending: false })
          .limit(5)

        if (ordersData) {
          userDetails.recent_orders = ordersData
        }
      }

      setSelectedUser(userDetails)
    } catch (error) {
      console.error('Error fetching user details:', error)
    } finally {
      setLoadingDetails(false)
    }
  }

  const customerCount = users.filter(u => u.role === 'customer').length
  const adminCount = users.filter(u => u.role === 'admin').length

  if (loading) {
    return (
      <div className="space-y-4 px-2 sm:px-0">
        <div className="animate-pulse">
          <div className="h-6 sm:h-8 bg-gray-200 rounded w-3/4 sm:w-1/4 mb-4"></div>
          <div className="h-8 sm:h-10 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="h-16 sm:h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4 sm:space-y-6 px-2 sm:px-0">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-xl sm:text-2xl font-bold text-gray-900">Gestión de Usuarios</h2>
          <p className="text-sm sm:text-base text-gray-600">Administra cuentas de clientes y crea nuevos usuarios</p>
        </div>
        <Button 
          onClick={() => setShowCreateForm(true)}
          className="w-full sm:w-auto"
          size="sm"
        >
          + Crear Nuevo Usuario
        </Button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Total de Usuarios</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900">{users.length}</p>
              </div>
              <Users className="w-5 h-5 sm:w-6 sm:h-6 text-gray-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Clientes</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900">{customerCount}</p>
              </div>
              <ShoppingCart className="w-5 h-5 sm:w-6 sm:h-6 text-gray-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="sm:col-span-2 lg:col-span-1">
          <CardContent className="p-3 sm:p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600">Administradores</p>
                <p className="text-lg sm:text-2xl font-bold text-gray-900">{adminCount}</p>
              </div>
              <Settings className="w-5 h-5 sm:w-6 sm:h-6 text-gray-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-3 sm:p-4">
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <div className="flex-1">
              <Input
                placeholder="Buscar por nombre, correo o ID de usuario..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full text-sm"
              />
            </div>
            <div className="w-full sm:w-48">
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                className="w-full h-9 sm:h-10 px-3 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">Todos los Roles</option>
                <option value="customer">Clientes</option>
                <option value="admin">Administradores</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Create User Form */}
      {showCreateForm && (
        <Card>
          <CardHeader className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg sm:text-xl">Crear Nuevo Usuario</CardTitle>
              <Button variant="ghost" size="sm" onClick={() => setShowCreateForm(false)}>
                Cancelar
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-4 sm:p-6 pt-0">
            <form onSubmit={handleCreateUser} className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="new_user_email" className="text-sm">Correo Electrónico</Label>
                  <Input
                    id="new_user_email"
                    type="email"
                    value={newUser.email}
                    onChange={(e) => setNewUser(prev => ({ ...prev, email: e.target.value }))}
                    className="text-sm"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new_user_name" className="text-sm">Nombre Completo</Label>
                  <Input
                    id="new_user_name"
                    value={newUser.full_name}
                    onChange={(e) => setNewUser(prev => ({ ...prev, full_name: e.target.value }))}
                    className="text-sm"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new_user_phone" className="text-sm">Teléfono (Opcional)</Label>
                  <Input
                    id="new_user_phone"
                    type="tel"
                    value={newUser.phone}
                    onChange={(e) => setNewUser(prev => ({ ...prev, phone: e.target.value }))}
                    className="text-sm"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new_user_role" className="text-sm">Rol</Label>
                  <select
                    id="new_user_role"
                    value={newUser.role}
                    onChange={(e) => setNewUser(prev => ({ ...prev, role: e.target.value as 'customer' | 'admin' }))}
                    className="w-full h-9 sm:h-10 px-3 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="customer">Cliente</option>
                    <option value="admin">Administrador</option>
                  </select>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row justify-end gap-2 sm:space-x-2">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setShowCreateForm(false)}
                  className="w-full sm:w-auto order-2 sm:order-1"
                  size="sm"
                >
                  Cancelar
                </Button>
                <Button 
                  type="submit"
                  className="w-full sm:w-auto order-1 sm:order-2"
                  size="sm"
                >
                  Crear Usuario
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* User Details Modal */}
      {selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
          <div className="bg-white rounded-lg w-full max-w-6xl max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-white border-b p-4 sm:p-6 flex items-center justify-between">
              <h2 className="text-lg sm:text-2xl font-bold text-gray-900">Detalles del Usuario</h2>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => setSelectedUser(null)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
            
            {loadingDetails ? (
              <div className="p-4 sm:p-6">
                <div className="animate-pulse space-y-4">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            ) : (
              <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
                {/* Basic Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                  <Card>
                    <CardHeader className="p-3 sm:p-4">
                      <CardTitle className="flex items-center gap-2 text-sm sm:text-base md:text-lg">
                        <User className="w-4 h-4 sm:w-5 sm:h-5" />
                        Información Personal
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-3 sm:p-4 pt-0 space-y-3 sm:space-y-4">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 sm:w-16 sm:h-16 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-lg sm:text-2xl font-semibold text-blue-600">
                            {selectedUser.full_name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <h3 className="text-base sm:text-lg font-medium break-words">{selectedUser.full_name}</h3>
                          <Badge className={selectedUser.role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}>
                            {selectedUser.role === 'admin' ? 'Administrador' : 'Cliente'}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="space-y-2 sm:space-y-3">
                        <div className="flex items-center gap-2">
                          <Mail className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" />
                          <span className="text-xs sm:text-sm break-all">{selectedUser.email}</span>
                        </div>
                        {selectedUser.phone && (
                          <div className="flex items-center gap-2">
                            <Phone className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" />
                            <span className="text-xs sm:text-sm">{selectedUser.phone}</span>
                          </div>
                        )}
                        <div className="flex items-center gap-2">
                          <Calendar className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" />
                          <span className="text-xs sm:text-sm">
                            Se unió el {new Date(selectedUser.created_at).toLocaleDateString()}
                          </span>
                        </div>
                        
                        {/* Additional Customer Profile Information */}
                        {selectedUser.role === 'customer' && selectedUser.customer_profile && (
                          <>
                            {selectedUser.customer_profile.preferred_name && (
                              <div className="flex items-center gap-2">
                                <User className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" />
                                <span className="text-xs sm:text-sm">
                                  Nombre Preferido: {selectedUser.customer_profile.preferred_name}
                                </span>
                              </div>
                            )}
                            {selectedUser.customer_profile.preferred_delivery_time && (
                              <div className="flex items-center gap-2">
                                <Calendar className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" />
                                <span className="text-xs sm:text-sm">
                                  Entrega Preferida: {
                                    selectedUser.customer_profile.preferred_delivery_time === 'morning' ? 'Mañana (8:00 - 12:00)' :
                                    selectedUser.customer_profile.preferred_delivery_time === 'afternoon' ? 'Tarde (12:00 - 18:00)' :
                                    selectedUser.customer_profile.preferred_delivery_time === 'evening' ? 'Noche (18:00 - 22:00)' :
                                    selectedUser.customer_profile.preferred_delivery_time === 'any' ? 'Cualquier hora' :
                                    selectedUser.customer_profile.preferred_delivery_time
                                  }
                                </span>
                              </div>
                            )}
                            {selectedUser.customer_profile.emergency_contact_name && (
                              <div className="flex items-center gap-2">
                                <User className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" />
                                <span className="text-xs sm:text-sm">
                                  Contacto de Emergencia: {selectedUser.customer_profile.emergency_contact_name}
                                </span>
                              </div>
                            )}
                            {selectedUser.customer_profile.emergency_contact_phone && (
                              <div className="flex items-center gap-2">
                                <Phone className="w-3 h-3 sm:w-4 sm:h-4 text-gray-500 flex-shrink-0" />
                                <span className="text-xs sm:text-sm">
                                  Teléfono de Emergencia: {selectedUser.customer_profile.emergency_contact_phone}
                                </span>
                              </div>
                            )}
                            {selectedUser.customer_profile.customer_tier && (
                              <div className="flex items-center gap-2">
                                <Badge className="text-xs">
                                  Nivel {selectedUser.customer_profile.customer_tier}
                                </Badge>
                              </div>
                            )}
                            {selectedUser.customer_profile.loyalty_points !== undefined && (
                              <div className="flex items-center gap-2">
                                <span className="text-xs sm:text-sm">
                                  Puntos de Lealtad: {selectedUser.customer_profile.loyalty_points}
                                </span>
                              </div>
                            )}
                            <div className="flex items-center gap-2">
                              <input
                                type="checkbox"
                                checked={selectedUser.customer_profile.marketing_opt_in || false}
                                disabled
                                className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <span className="text-xs sm:text-sm">
                                Marketing habilitado
                              </span>
                            </div>
                          </>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {selectedUser.role === 'customer' && (
                    <Card>
                      <CardHeader className="p-3 sm:p-4">
                        <CardTitle className="flex items-center gap-2 text-sm sm:text-base md:text-lg">
                          <Wallet className="w-4 h-4 sm:w-5 sm:h-5" />
                          Billetera y Estadísticas
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-3 sm:p-4 pt-0 space-y-3 sm:space-y-4">
                        <div className="bg-green-50 p-3 sm:p-4 rounded-lg">
                          <p className="text-xs sm:text-sm text-gray-600">Saldo Actual</p>
                          <p className="text-xl sm:text-2xl font-bold text-green-600">
                            ${selectedUser.wallet_balance?.toFixed(2) || '0.00'} MXN
                          </p>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3 sm:gap-4">
                          <div className="bg-blue-50 p-2 sm:p-3 rounded-lg text-center">
                            <p className="text-xs sm:text-sm text-gray-600">Total de Pedidos</p>
                            <p className="text-lg sm:text-xl font-bold text-blue-600">
                              {selectedUser.orders_count || 0}
                            </p>
                          </div>
                          <div className="bg-purple-50 p-2 sm:p-3 rounded-lg text-center">
                            <p className="text-xs sm:text-sm text-gray-600">Total Gastado</p>
                            <p className="text-lg sm:text-xl font-bold text-purple-600">
                              ${selectedUser.total_spent?.toFixed(2) || '0.00'}
                            </p>
                          </div>
                        </div>
                        
                        {selectedUser.last_order && (
                          <div className="text-xs sm:text-sm text-gray-600">
                            Último pedido: {new Date(selectedUser.last_order).toLocaleDateString()}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  )}
                </div>

                {/* Recent Transactions */}
                {selectedUser.role === 'customer' && selectedUser.recent_transactions && selectedUser.recent_transactions.length > 0 && (
                  <Card>
                    <CardHeader className="p-3 sm:p-4">
                      <CardTitle className="flex items-center gap-2 text-sm sm:text-base md:text-lg">
                        <CreditCard className="w-4 h-4 sm:w-5 sm:h-5" />
                        Transacciones Recientes
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-3 sm:p-4 pt-0">
                      <div className="space-y-2 sm:space-y-3">
                        {selectedUser.recent_transactions.map((transaction) => (
                          <div key={transaction.id} className="flex flex-col md:flex-row md:items-start md:justify-between p-3 border rounded-lg gap-3">
                            <div className="flex-1 min-w-0">
                              <p className="font-medium text-sm sm:text-base line-clamp-2 md:line-clamp-1">
                                {transaction.description.length > 50 
                                  ? `${transaction.description.substring(0, 50)}...` 
                                  : transaction.description}
                              </p>
                              <p className="text-xs sm:text-sm text-gray-600 mt-1">
                                {new Date(transaction.created_at).toLocaleDateString()}
                              </p>
                            </div>
                            <div className="flex justify-between md:flex-col md:text-right items-center md:items-end gap-2 md:min-w-[120px]">
                              <p className={`font-bold text-sm sm:text-base whitespace-nowrap ${
                                transaction.type === 'deposit' ? 'text-green-600' : 'text-red-600'
                              }`}>
                                {transaction.type === 'deposit' ? '+' : '-'}${transaction.amount.toFixed(2)}
                              </p>
                              <Badge 
                                variant={transaction.status === 'completed' ? 'default' : 'secondary'}
                                className="text-xs"
                              >
                                {transaction.status}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Recent Orders */}
                {selectedUser.role === 'customer' && selectedUser.recent_orders && selectedUser.recent_orders.length > 0 && (
                  <Card>
                    <CardHeader className="p-3 sm:p-4">
                      <CardTitle className="flex items-center gap-2 text-sm sm:text-base md:text-lg">
                        <ShoppingCart className="w-4 h-4 sm:w-5 sm:h-5" />
                        Pedidos Recientes
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-3 sm:p-4 pt-0">
                      <div className="space-y-2 sm:space-y-3">
                        {selectedUser.recent_orders.map((order) => (
                          <div key={order.id} className="p-2 sm:p-3 border rounded-lg">
                            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2 gap-2">
                              <p className="font-medium text-sm sm:text-base">Pedido #{order.id.slice(0, 8)}</p>
                              <div className="flex justify-between md:flex-col md:text-right items-center md:items-end gap-2">
                                <p className="font-bold text-sm sm:text-base">${order.total_cost.toFixed(2)}</p>
                                <Badge variant="outline" className="text-xs">{order.status}</Badge>
                              </div>
                            </div>
                            <div className="text-xs sm:text-sm text-gray-600 space-y-1">
                              <p className="break-words"><span className="font-medium">Desde:</span> {order.pickup_address}</p>
                              <p className="break-words"><span className="font-medium">Hasta:</span> {order.delivery_address}</p>
                              <p><span className="font-medium">Fecha:</span> {new Date(order.created_at).toLocaleDateString()}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Users List */}
      <div className="space-y-3 sm:space-y-4">
        {filteredUsers.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8 sm:py-12 px-4">
              <div className="mb-4">
                <Users className="w-12 h-12 sm:w-16 sm:h-16 mx-auto text-gray-400" />
              </div>
              <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">No se encontraron usuarios</h3>
              <p className="text-sm sm:text-base text-gray-600">
                {searchTerm || roleFilter !== 'all' 
                  ? 'Intenta ajustar tu búsqueda o criterios de filtro'
                  : 'Aún no se han creado usuarios'
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredUsers.map((user) => (
            <Card key={user.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4 sm:p-6">
                <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                  {/* User Info */}
                  <div className="flex items-center space-x-3 sm:space-x-4 flex-1">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-sm sm:text-lg font-semibold text-blue-600">
                        {user.full_name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div className="min-w-0 flex-1">
                      <h3 className="text-base sm:text-lg font-medium text-gray-900 truncate">{user.full_name}</h3>
                      <p className="text-sm sm:text-base text-gray-600 truncate">{user.email}</p>
                      {user.phone && (
                        <p className="text-xs sm:text-sm text-gray-500 truncate">{user.phone}</p>
                      )}
                    </div>
                  </div>
                  
                  {/* Stats and Actions */}
                  <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
                    {/* Role Badge */}
                    <div className="flex justify-between sm:block">
                      <Badge className={user.role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}>
                        {user.role === 'admin' ? 'Administrador' : 'Cliente'}
                      </Badge>
                                              <p className="text-xs sm:text-sm text-gray-500 sm:mt-1">
                          Se unió el {new Date(user.created_at).toLocaleDateString()}
                        </p>
                    </div>
                    
                    {/* Customer Stats */}
                    {user.role === 'customer' && (
                      <div className="grid grid-cols-2 sm:block gap-2 sm:gap-0 text-center sm:text-right">
                        <div>
                          <p className="text-xs sm:text-sm font-medium text-gray-900">
                            {user.orders_count || 0} pedidos
                          </p>
                          <p className="text-xs sm:text-sm text-gray-600">
                            ${user.total_spent?.toFixed(2) || '0.00'} gastado
                          </p>
                        </div>
                        {user.last_order && (
                          <div className="col-span-2 sm:col-span-1">
                            <p className="text-xs text-gray-500">
                              Último pedido: {new Date(user.last_order).toLocaleDateString()}
                            </p>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {/* Action Button */}
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleViewDetails(user)}
                      className="w-full sm:w-auto text-xs sm:text-sm"
                    >
                      Ver Detalles
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      <div className="text-center text-xs sm:text-sm text-gray-500">
        Mostrando {filteredUsers.length} de {users.length} usuarios
      </div>
    </div>
  )
}
