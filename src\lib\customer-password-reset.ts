import { createClient } from '@/utils/supabase/server'
import { auditLogger, JWTContext } from '@/lib/audit-logger'

export interface CustomerPasswordResetData {
  user_id: string
  email: string
  role: 'customer' | 'admin'
  full_name?: string
  phone?: string
  customer_profile?: {
    preferred_name?: string
    emergency_contact_name?: string
    emergency_contact_phone?: string
    preferred_delivery_time?: string
    marketing_opt_in?: boolean
    customer_tier?: string
    loyalty_points?: number
    total_orders?: number
    total_spent?: number
  }
  wallet_balance?: number
  recent_orders_count?: number
  last_login?: string
}

export interface PasswordResetNotificationData {
  email: string
  full_name?: string
  preferred_name?: string
  emergency_contact_name?: string
  emergency_contact_phone?: string
  customer_tier?: string
  loyalty_points?: number
  reset_timestamp: string
  ip_address?: string
  user_agent?: string
}

export class CustomerPasswordResetService {
  private static instance: CustomerPasswordResetService
  
  private constructor() {}
  
  public static getInstance(): CustomerPasswordResetService {
    if (!CustomerPasswordResetService.instance) {
      CustomerPasswordResetService.instance = new CustomerPasswordResetService()
    }
    return CustomerPasswordResetService.instance
  }

  /**
   * Get comprehensive customer data for password reset
   */
  async getCustomerResetData(email: string): Promise<CustomerPasswordResetData | null> {
    try {
      const supabase = await createClient()

      // Get user profile from profiles table (updated for mouvers project yhiffsfdiqnjdqmnlghp)
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('id, email, full_name, phone, role, created_at')
        .eq('email', email.toLowerCase())
        .single()

      if (profileError || !profile) {
        console.log('User not found or error:', profileError)
        return null
      }

      // Get customer-specific data if user is a customer
      let customerProfile = null
      let walletBalance = 0
      let recentOrdersCount = 0

      if (profile.role === 'customer' || profile.role === 'admin') {
        // Get customer profile if it exists
        const { data: customerData, error: customerError } = await supabase
          .from('customer_profiles')
          .select('*')
          .eq('user_id', profile.id)
          .single()

        // Get wallet balance if user has a wallet
        const { data: walletData, error: walletError } = await supabase
          .from('user_wallets')
          .select('balance')
          .eq('user_id', profile.id)
          .single()

        // Get recent orders count
        const { data: ordersData, error: ordersError } = await supabase
          .from('orders')
          .select('id')
          .eq('customer_id', profile.id)
          .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days

        if (customerData && !customerError) {
          customerProfile = customerData
        } else {
          // Create a basic customer profile structure if none exists
          customerProfile = {
            preferred_name: profile.full_name?.split(' ')[0] || 'User',
            customer_tier: 'standard',
            loyalty_points: 0,
            total_orders: 0,
            total_spent: 0
          }
        }

        walletBalance = walletData?.balance || 0
        recentOrdersCount = ordersData?.length || 0
      }

      return {
        user_id: profile.id,
        email: profile.email,
        role: profile.role as 'customer' | 'admin',
        full_name: profile.full_name || 'User',
        phone: profile.phone,
        customer_profile: customerProfile,
        wallet_balance: walletBalance,
        recent_orders_count: recentOrdersCount,
        last_login: profile.created_at
      }
    } catch (error) {
      console.error('Error getting customer reset data:', error)
      return null
    }
  }

  /**
   * Send customer-specific password reset notification
   * This function sends additional customer-specific notifications after Supabase's built-in email
   */
  async sendCustomerResetNotification(data: PasswordResetNotificationData): Promise<boolean> {
    try {
      // Log the notification attempt
      await auditLogger.log({
        event_type: 'password_reset_requested',
        email: data.email,
        ip_address: data.ip_address,
        user_agent: data.user_agent,
        success: true,
        metadata: {
          customer_tier: data.customer_tier,
          loyalty_points: data.loyalty_points,
          has_emergency_contact: !!data.emergency_contact_name,
          notification_type: 'customer_specific'
        }
      })

      // Send customer-specific notification (in addition to Supabase's built-in email)
      // This could be used for enhanced notifications, SMS, or custom email templates
      const notificationData = {
        email: data.email,
        name: data.preferred_name || data.full_name,
        customer_tier: data.customer_tier,
        loyalty_points: data.loyalty_points,
        emergency_contact: data.emergency_contact_name,
        timestamp: data.reset_timestamp,
        ip_address: data.ip_address,
        user_agent: data.user_agent
      }

      // TODO: Implement custom email service integration here
      // Examples: SendGrid, Mailgun, AWS SES, etc.
      // For now, we log the data for debugging
      console.log('📧 Customer-specific password reset notification prepared:', notificationData)

      // Future implementation could include:
      // - Custom HTML email templates
      // - SMS notifications for VIP customers
      // - Slack/Discord notifications for admins
      // - Multi-language support based on customer preferences

      return true
    } catch (error) {
      console.error('❌ Error sending customer reset notification:', error)
      return false
    }
  }

  /**
   * Check if customer should receive additional security measures
   */
  shouldApplyEnhancedSecurity(customerData: CustomerPasswordResetData): boolean {
    // Apply enhanced security for high-value customers
    if (customerData.wallet_balance && customerData.wallet_balance > 1000) {
      return true
    }

    // Apply enhanced security for premium customers
    if (customerData.customer_profile?.customer_tier === 'premium' || 
        customerData.customer_profile?.customer_tier === 'vip') {
      return true
    }

    // Apply enhanced security for customers with many recent orders
    if (customerData.recent_orders_count && customerData.recent_orders_count > 10) {
      return true
    }

    return false
  }

  /**
   * Get customer-specific password requirements
   */
  getCustomerPasswordRequirements(customerData: CustomerPasswordResetData) {
    const baseRequirements = {
      minLength: 8,
      maxLength: 128,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      preventCommonPasswords: true,
      preventPersonalInfo: true,
    }

    // Enhanced requirements for high-value customers
    if (this.shouldApplyEnhancedSecurity(customerData)) {
      return {
        ...baseRequirements,
        minLength: 12, // Longer password for high-value accounts
        requireSpecialChars: true,
        preventPersonalInfo: true,
        additionalChecks: [
          'no_sequential_chars',
          'no_repeated_chars',
          'no_dictionary_words'
        ]
      }
    }

    return baseRequirements
  }

  /**
   * Send emergency contact notification for high-value accounts
   */
  async notifyEmergencyContact(customerData: CustomerPasswordResetData, resetData: {
    ip_address?: string
    user_agent?: string
    timestamp: string
  }): Promise<boolean> {
    if (!this.shouldApplyEnhancedSecurity(customerData) ||
        !customerData.customer_profile?.emergency_contact_name ||
        !customerData.customer_profile?.emergency_contact_phone) {
      return false
    }

    try {
      // Log the emergency contact notification
      await auditLogger.log({
        event_type: 'password_reset_requested',
        user_id: customerData.user_id,
        email: customerData.email,
        ip_address: resetData.ip_address,
        user_agent: resetData.user_agent,
        success: true,
        metadata: {
          emergency_contact_notified: true,
          emergency_contact_name: customerData.customer_profile.emergency_contact_name,
          customer_tier: customerData.customer_profile.customer_tier,
          wallet_balance: customerData.wallet_balance,
          notification_reason: 'high_value_account_security'
        }
      })

      const emergencyNotificationData = {
        customer_name: customerData.full_name,
        customer_email: customerData.email,
        emergency_contact: customerData.customer_profile.emergency_contact_name,
        emergency_phone: customerData.customer_profile.emergency_contact_phone,
        reset_timestamp: resetData.timestamp,
        ip_address: resetData.ip_address,
        user_agent: resetData.user_agent,
        wallet_balance: customerData.wallet_balance,
        customer_tier: customerData.customer_profile.customer_tier
      }

      // TODO: Implement emergency contact notification service
      // This could include SMS via Twilio, WhatsApp, or email
      console.log('🚨 Emergency contact notification prepared:', emergencyNotificationData)

      // Future implementation could include:
      // - SMS via Twilio: `await twilioClient.messages.create({...})`
      // - WhatsApp Business API
      // - Email to emergency contact
      // - Push notification to mobile app

      return true
    } catch (error) {
      console.error('❌ Error notifying emergency contact:', error)
      return false
    }
  }

  /**
   * Create customer-specific audit log entry with JWT context
   */
  async logCustomerPasswordReset(customerData: CustomerPasswordResetData, resetData: {
    ip_address?: string
    user_agent?: string
    success: boolean
    error_message?: string
    jwt_context?: JWTContext
  }): Promise<void> {
    await auditLogger.log({
      event_type: resetData.success ? 'password_reset_completed' : 'password_reset_failed',
      user_id: customerData.user_id,
      email: customerData.email,
      ip_address: resetData.ip_address,
      user_agent: resetData.user_agent,
      success: resetData.success,
      error_message: resetData.error_message,
      jwt_context: resetData.jwt_context,
      metadata: {
        customer_role: customerData.role,
        customer_tier: customerData.customer_profile?.customer_tier,
        wallet_balance: customerData.wallet_balance,
        recent_orders_count: customerData.recent_orders_count,
        loyalty_points: customerData.customer_profile?.loyalty_points,
        has_emergency_contact: !!customerData.customer_profile?.emergency_contact_name,
        enhanced_security_applied: this.shouldApplyEnhancedSecurity(customerData),
        // JWT-specific customer analysis
        customer_auth_level: resetData.jwt_context?.aal || 'unknown',
        customer_has_mfa: resetData.jwt_context?.aal === 'aal2',
        customer_session_age: resetData.jwt_context?.expires_at ?
          Math.max(0, resetData.jwt_context.expires_at - Math.floor(Date.now() / 1000)) : null
      }
    })
  }
}

// Export singleton instance
export const customerPasswordResetService = CustomerPasswordResetService.getInstance()
