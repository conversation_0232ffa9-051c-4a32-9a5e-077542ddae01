"use client"

import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { LogoutButton } from '@/components/auth/logout-button'

export function Navbar() {
  const { user, loading } = useAuth()

  return (
    <nav className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link href={user ? "/dashboard" : "/"} className="text-xl font-bold text-black">
              Mouvers
            </Link>
          </div>
          
          <div className="flex items-center space-x-4">
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black"></div>
            ) : user ? (
              <>
                <span className="text-sm text-gray-600">
                  {user.email}
                </span>
                <LogoutButton className="text-sm">
                  Sign Out
                </LogoutButton>
              </>
            ) : (
              <>
                <Link href="/register">
                  <Button variant="ghost">
                    Sign Up
                  </Button>
                </Link>
                <Link href="/">
                  <Button>
                    Sign In
                  </Button>
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  )
} 