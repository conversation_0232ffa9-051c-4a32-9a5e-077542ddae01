'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { PasswordResetErrorAlert, PasswordResetSuccessAlert } from '@/components/ui/error-alert'

export function ResetPasswordForm() {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const { resetPassword } = useAuth()

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      // Use the new API endpoint with rate limiting
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (!response.ok) {
        if (response.status === 429) {
          // Rate limit error
          const retryAfter = data.retryAfter
          if (retryAfter) {
            setError(`${data.error} Intenta de nuevo en ${retryAfter} minuto${retryAfter > 1 ? 's' : ''}.`)
          } else {
            setError(data.error || 'Demasiados intentos. Por favor intenta más tarde.')
          }
        } else {
          setError(data.error || 'Ocurrió un error inesperado')
        }
      } else {
        setSuccess(true)
      }
    } catch (error) {
      console.error('Password reset request error:', error)
      setError('Error de conexión. Por favor verifica tu conexión a internet.')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="space-y-6 bg-gradient-to-br from-blue-900/20 to-green-900/20 backdrop-blur-sm border border-blue-400/30 rounded-lg p-6 shadow-xl w-full max-w-md">
        <PasswordResetSuccessAlert
          message="Revisa tu correo electrónico para las instrucciones de restablecimiento de contraseña."
        />
        <div className="text-center">
          <Link
            href="/login"
            className="text-green-300 hover:text-blue-300 hover:underline font-medium transition-colors"
          >
            Volver al inicio de sesión
          </Link>
        </div>
      </div>
    )
  }

  return (
    <form onSubmit={handleEmailSubmit} className="space-y-6 bg-gradient-to-br from-blue-900/20 to-green-900/20 backdrop-blur-sm border border-blue-400/30 rounded-lg p-6 shadow-xl w-full max-w-md">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-white">Restablecer contraseña</h2>
        <p className="text-gray-300">
          Ingresa tu correo electrónico y te enviaremos un enlace de restablecimiento
        </p>
      </div>
      
      {error && (
        <PasswordResetErrorAlert
          error={error}
          onDismiss={() => setError('')}
        />
      )}
      
      <div className="space-y-2">
        <Label htmlFor="email" className="text-white">Correo electrónico *</Label>
        <Input
          id="email"
          type="email"
          placeholder="<EMAIL>"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          disabled={loading}
        />
      </div>

      <Button 
        type="submit" 
        className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white border-0" 
        disabled={loading}
      >
        {loading ? 'Enviando...' : 'Enviar enlace de restablecimiento'}
      </Button>

      <div className="text-center">
        <p className="text-sm text-white">
          ¿Recuerdas tu contraseña?{' '}
          <Link 
            href="/login" 
            className="text-green-300 hover:text-blue-300 hover:underline font-medium transition-colors"
          >
            Inicia sesión aquí
          </Link>
        </p>
      </div>
    </form>
  )
} 