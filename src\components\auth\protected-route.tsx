'use client'

import { useEffect, ReactNode, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Spinner } from '@/components/ui/spinner'

interface ProtectedRouteProps {
  children: ReactNode
  requireAuth?: boolean
  requireAdmin?: boolean
  requireCustomer?: boolean
  redirectTo?: string
  fallback?: ReactNode
}

export function ProtectedRoute({
  children,
  requireAuth = true,
  requireAdmin = false,
  requireCustomer = false,
  redirectTo,
  fallback,
}: ProtectedRouteProps) {
  const { user, profile, loading, isAdmin, isCustomer, dbError } = useAuth()
  const router = useRouter()

  const handleRedirect = useCallback((path: string) => {
    router.push(path)
  }, [router])

  useEffect(() => {
    if (loading) return

    // If authentication is required but user is not logged in
    if (requireAuth && !user) {
      handleRedirect(redirectTo || '/')
      return
    }

    // If no authentication is required but user is logged in
    if (!requireAuth && user && !dbError) {
      handleRedirect('/dashboard')
      return
    }

    // If admin role is required but user is not admin
    if (requireAdmin && user && profile && !isAdmin) {
      handleRedirect('/dashboard')
      return
    }

    // If customer role is required but user is not customer
    if (requireCustomer && user && profile && !isCustomer) {
      handleRedirect('/dashboard')
      return
    }
  }, [
    user,
    profile,
    loading,
    isAdmin,
    isCustomer,
    dbError,
    requireAuth,
    requireAdmin,
    requireCustomer,
    handleRedirect,
    redirectTo
  ])

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <Spinner className="mx-auto mb-4" />
            <p className="text-gray-600">Verificando autenticación...</p>
          </div>
        </div>
      )
    )
  }

  // Show error state if there's a database error
  if (dbError && requireAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error de conexión a la base de datos</p>
          <p className="text-gray-600">{dbError}</p>
        </div>
      </div>
    )
  }

  // Check conditions and decide whether to render children
  if (requireAuth && !user) {
    return null // Will redirect
  }

  if (!requireAuth && user && !dbError) {
    return null // Will redirect to dashboard
  }

  if (requireAdmin && user && profile && !isAdmin) {
    return null // Will redirect
  }

  if (requireCustomer && user && profile && !isCustomer) {
    return null // Will redirect
  }

  // If we need profile but it's not loaded yet, show loading
  if (requireAuth && user && !profile && !dbError) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <Spinner className="mx-auto mb-4" />
            <p className="text-gray-600">Cargando perfil...</p>
          </div>
        </div>
      )
    )
  }

  // All conditions met, render children
  return <>{children}</>
} 