/**
 * Complete Password Reset Flow Test
 * Run this after re-enabling API keys and configuring email templates
 */

const { createClient } = require('@supabase/supabase-js')

// Project configuration for mouvers (yhiffsfdiqnjdqmnlghp)
const supabaseUrl = 'https://yhiffsfdiqnjdqmnlghp.supabase.co'
const supabaseAnonKey = 'YOUR_ANON_KEY_HERE' // Replace with actual key after re-enabling

const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    flowType: 'pkce',
    persistSession: false,
    detectSessionInUrl: false,
    autoRefreshToken: false,
  },
})

// Test configuration
const TEST_CONFIG = {
  email: '<EMAIL>', // Admin user
  redirectUrl: 'http://localhost:3000/reset-password/confirm',
  // Alternative test email: '<EMAIL>' (customer user)
}

async function testUserExists() {
  console.log('🔍 Step 1: Checking if test user exists...')
  
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, email, full_name, role, created_at')
      .eq('email', TEST_CONFIG.email)
      .single()
    
    if (error) {
      console.error('❌ Error checking user:', error.message)
      return false
    }
    
    if (data) {
      console.log('✅ User found:')
      console.log(`   📧 Email: ${data.email}`)
      console.log(`   👤 Name: ${data.full_name}`)
      console.log(`   🏷️  Role: ${data.role}`)
      console.log(`   📅 Created: ${new Date(data.created_at).toLocaleDateString()}`)
      return true
    }
    
    console.log('❌ User not found')
    return false
    
  } catch (error) {
    console.error('❌ Unexpected error:', error.message)
    return false
  }
}

async function testPasswordResetRequest() {
  console.log('\n📧 Step 2: Testing password reset email request...')
  
  try {
    const { data, error } = await supabase.auth.resetPasswordForEmail(
      TEST_CONFIG.email,
      {
        redirectTo: TEST_CONFIG.redirectUrl,
      }
    )
    
    if (error) {
      console.error('❌ Password reset request failed:')
      console.error(`   Message: ${error.message}`)
      console.error(`   Status: ${error.status || 'N/A'}`)
      return false
    }
    
    console.log('✅ Password reset request successful!')
    console.log('📊 Response data:', data || 'No data returned (normal for security)')
    console.log(`📧 Email should be sent to: ${TEST_CONFIG.email}`)
    console.log(`🔗 Reset link will redirect to: ${TEST_CONFIG.redirectUrl}`)
    
    return true
    
  } catch (error) {
    console.error('❌ Unexpected error:', error.message)
    return false
  }
}

async function testCustomerDataRetrieval() {
  console.log('\n🔍 Step 3: Testing customer data retrieval...')
  
  try {
    // Simulate what the API route does
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, email, full_name, phone, role, created_at')
      .eq('email', TEST_CONFIG.email.toLowerCase())
      .single()

    if (profileError || !profile) {
      console.error('❌ Profile not found:', profileError?.message)
      return false
    }

    console.log('✅ Profile data retrieved:')
    console.log(`   ID: ${profile.id}`)
    console.log(`   Email: ${profile.email}`)
    console.log(`   Name: ${profile.full_name}`)
    console.log(`   Role: ${profile.role}`)

    // Check for customer-specific data
    if (profile.role === 'customer' || profile.role === 'admin') {
      const { data: customerData } = await supabase
        .from('customer_profiles')
        .select('*')
        .eq('user_id', profile.id)
        .single()

      const { data: walletData } = await supabase
        .from('user_wallets')
        .select('balance')
        .eq('user_id', profile.id)
        .single()

      console.log('📊 Customer-specific data:')
      console.log(`   Customer Profile: ${customerData ? '✅ Found' : '❌ Not found'}`)
      console.log(`   Wallet Balance: ${walletData?.balance || 0}`)
    }

    return true
    
  } catch (error) {
    console.error('❌ Error retrieving customer data:', error.message)
    return false
  }
}

async function checkEmailConfiguration() {
  console.log('\n⚙️  Step 4: Checking email configuration...')
  
  console.log('📋 Email setup checklist:')
  console.log('   🔑 API Keys: Re-enable in dashboard')
  console.log('   📧 SMTP: Configure in Auth settings')
  console.log('   📝 Template: Upload custom template')
  console.log('   🌐 Site URL: Set redirect URLs')
  
  console.log('\n🔗 Configuration URLs:')
  console.log(`   API Keys: https://supabase.com/dashboard/project/yhiffsfdiqnjdqmnlghp/settings/api`)
  console.log(`   Auth Settings: https://supabase.com/dashboard/project/yhiffsfdiqnjdqmnlghp/settings/auth`)
  console.log(`   Email Templates: https://supabase.com/dashboard/project/yhiffsfdiqnjdqmnlghp/auth/templates`)
  console.log(`   Logs: https://supabase.com/dashboard/project/yhiffsfdiqnjdqmnlghp/logs/auth-logs`)
}

async function runCompleteTest() {
  console.log('🚀 Starting Complete Password Reset Flow Test')
  console.log('=' .repeat(60))
  console.log(`📧 Test Email: ${TEST_CONFIG.email}`)
  console.log(`🌐 Project: mouvers (yhiffsfdiqnjdqmnlghp)`)
  console.log(`🔗 Redirect URL: ${TEST_CONFIG.redirectUrl}`)
  console.log('=' .repeat(60))
  
  // Step 1: Check user exists
  const userExists = await testUserExists()
  if (!userExists) {
    console.log('\n❌ Test failed: User does not exist')
    return
  }
  
  // Step 2: Test password reset request
  const resetRequested = await testPasswordResetRequest()
  if (!resetRequested) {
    console.log('\n❌ Test failed: Could not request password reset')
    console.log('\n💡 Possible issues:')
    console.log('   - API keys not re-enabled')
    console.log('   - SMTP not configured')
    console.log('   - Email template issues')
    return
  }
  
  // Step 3: Test customer data retrieval
  const dataRetrieved = await testCustomerDataRetrieval()
  if (!dataRetrieved) {
    console.log('\n⚠️  Warning: Customer data retrieval failed')
  }
  
  // Step 4: Show configuration info
  await checkEmailConfiguration()
  
  console.log('\n' + '=' .repeat(60))
  console.log('✅ Test completed successfully!')
  console.log('📧 Check your email inbox (including spam folder)')
  console.log('🔗 Click the reset link to complete the flow')
  console.log('=' .repeat(60))
  
  console.log('\n📋 Next steps:')
  console.log('1. Check email delivery')
  console.log('2. Click reset link')
  console.log('3. Enter new password')
  console.log('4. Verify login with new password')
}

// Handle API key check
if (supabaseAnonKey === 'YOUR_ANON_KEY_HERE') {
  console.error('❌ Please update the supabaseAnonKey with your actual API key')
  console.log('📋 Steps to get API key:')
  console.log('1. Go to: https://supabase.com/dashboard/project/yhiffsfdiqnjdqmnlghp/settings/api')
  console.log('2. Re-enable legacy API keys')
  console.log('3. Copy the anon key and replace YOUR_ANON_KEY_HERE in this file')
  process.exit(1)
}

// Run the test
runCompleteTest().catch(console.error)
