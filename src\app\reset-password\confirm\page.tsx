'use client'

import { Suspense, useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { ConfirmResetPasswordForm } from '@/components/auth/confirm-reset-password-form'
import { AuthLayout } from '@/components/auth/auth-layout'

export default function ConfirmResetPasswordPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center"><div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black" /></div>}>
      <ConfirmResetPasswordContent />
    </Suspense>
  )
}

function ConfirmResetPasswordContent() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isValidReset, setIsValidReset] = useState(false)
  const [processingAuth, setProcessingAuth] = useState(true)

  // Process auth callback from URL
  useEffect(() => {
    const processAuthCallback = async () => {
      // For ECC (P-256) JWT keys, let Supabase handle the session detection automatically
      if (searchParams.get('code') || searchParams.get('token_hash') || searchParams.get('access_token')) {
        // With ECC keys and detectSessionInUrl: true, Supabase automatically
        // processes URL parameters and establishes the session
        await new Promise(resolve => setTimeout(resolve, 100)) // Small delay for processing
      }
      setProcessingAuth(false)
    }
    processAuthCallback()
  }, [searchParams])

  // Check if we have valid reset parameters
  useEffect(() => {
    if (processingAuth) return // Wait for auth processing

    const code = searchParams.get('code') || searchParams.get('token_hash')

    if (code) {
      setIsValidReset(true)
    } else {
      // Invalid or missing parameters, redirect to reset request page
      router.push('/reset-password')
    }
  }, [searchParams, router, processingAuth])

  useEffect(() => {
    // Only redirect to dashboard if user is authenticated AND not in valid reset flow
    if (!loading && !processingAuth && user && !isValidReset) {
      router.push('/dashboard')
    }
  }, [user, loading, router, isValidReset, processingAuth])

  if (loading || processingAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black" />
      </div>
    )
  }

  // Show reset form if we have valid reset code, regardless of user session
  if (!isValidReset) {
    return null // Will redirect to /reset-password
  }

  return (
    <AuthLayout>
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Mouvers</h1>
          <p className="text-gray-300">Establece tu nueva contraseña</p>
        </div>
        <ConfirmResetPasswordForm />
      </div>
    </AuthLayout>
  )
} 