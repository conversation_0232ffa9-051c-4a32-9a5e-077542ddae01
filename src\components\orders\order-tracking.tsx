'use client'

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface Order {
  id: string
  status: 'pending' | 'confirmed' | 'in-transit' | 'delivered' | 'cancelled'
  pickup_address: {
    street_address: string
    city: string
    state: string
    postal_code: string
    contact_name: string
    contact_phone: string
  }
  delivery_addresses: {
    street_address: string
    city: string
    state: string
    postal_code: string
    recipient_name: string
    phone: string
  }[]
  package_details: {
    description: string
    weight: string
    dimensions: string
    value: string
    special_instructions?: string
  }
  total_cost: number
  created_at: string
  updated_at: string
}

interface OrderTrackingProps {
  orders: Order[]
  loading?: boolean
}

const statusConfig = {
  pending: {
    label: 'Pendiente',
    color: 'bg-yellow-100 text-yellow-800',
    icon: '⏳',
    description: 'El pedido está siendo procesado'
  },
  confirmed: {
    label: 'Confirmado',
    color: 'bg-blue-100 text-blue-800',
    icon: '✅',
    description: 'Pedido confirmado y listo para recoger'
  },
  'in-transit': {
    label: 'En Tránsito',
    color: 'bg-orange-100 text-orange-800',
    icon: '🚚',
    description: 'El paquete está en camino'
  },
  delivered: {
    label: 'Entregado',
    color: 'bg-green-100 text-green-800',
    icon: '📦',
    description: 'Paquete entregado exitosamente'
  },
  cancelled: {
    label: 'Cancelado',
    color: 'bg-red-100 text-red-800',
    icon: '❌',
    description: 'El pedido ha sido cancelado'
  }
}

export function OrderTracking({ orders, loading }: OrderTrackingProps) {
  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (orders.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <div className="text-6xl mb-4">📦</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Aún no hay pedidos</h3>
          <p className="text-gray-600">Crea tu primer pedido para comenzar a rastrear entregas</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {orders.map((order) => {
        const config = statusConfig[order.status]
        return (
          <Card key={order.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">Pedido #{order.id.slice(-8)}</CardTitle>
                  <CardDescription>
                    Creado {new Date(order.created_at).toLocaleDateString()}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-lg">{config.icon}</span>
                  <Badge className={config.color}>
                    {config.label}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Pickup Info */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">📍 Recogida</h4>
                  <p className="text-sm text-gray-600">
                    {order.pickup_address?.street_address}
                    <br />
                    {order.pickup_address?.city}, {order.pickup_address?.state}
                  </p>
                </div>

                {/* Delivery Info */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">🚚 Entrega</h4>
                  {order.delivery_addresses?.map((addr, index) => (
                    <p key={index} className="text-sm text-gray-600 mb-1">
                      {addr.street_address}
                      <br />
                      {addr.city}, {addr.state}
                    </p>
                  ))}
                </div>

                {/* Package Info */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">📦 Paquete</h4>
                  <p className="text-sm text-gray-600">
                    {order.package_details?.description}
                    <br />
                    Peso: {order.package_details?.weight}kg
                  </p>
                  {order.total_cost && (
                    <p className="text-sm font-medium text-gray-900 mt-2">
                      ${order.total_cost.toFixed(2)} MXN
                    </p>
                  )}
                </div>
              </div>

              {/* Status Progress */}
              <div className="mt-6">
                <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                  <span>Progreso del Pedido</span>
                  <span>{config.description}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-300 ${
                      order.status === 'pending' ? 'w-1/4 bg-yellow-500' :
                      order.status === 'confirmed' ? 'w-1/2 bg-blue-500' :
                      order.status === 'in-transit' ? 'w-3/4 bg-orange-500' :
                      order.status === 'delivered' ? 'w-full bg-green-500' :
                      'w-1/4 bg-red-500'
                    }`}
                  ></div>
                </div>
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>Pendiente</span>
                  <span>Confirmado</span>
                  <span>En Tránsito</span>
                  <span>Entregado</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end mt-4 space-x-2">
                <button className="text-sm text-blue-600 hover:text-blue-800">
                  Ver Detalles
                </button>
                {order.status !== 'delivered' && order.status !== 'cancelled' && (
                  <button className="text-sm text-red-600 hover:text-red-800">
                    Cancelar Pedido
                  </button>
                )}
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
} 