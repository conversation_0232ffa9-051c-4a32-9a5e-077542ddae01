import { createClient } from '@/utils/supabase/server'

export interface JWTContext {
  access_token?: string
  refresh_token?: string
  expires_at?: number
  token_type?: string
  user_id?: string
  aal?: string // Authenticator Assurance Level
  amr?: Array<{ method: string; timestamp: number }> // Authentication Methods Reference
  session_id?: string
  is_anonymous?: boolean
  token_expires_in?: number
  refresh_token_expires_in?: number
}

export interface AuditLogEntry {
  event_type: 'password_reset_requested' | 'password_reset_completed' | 'password_reset_failed' | 'rate_limit_exceeded' | 'invalid_reset_attempt'
  user_id?: string
  email?: string
  ip_address?: string
  user_agent?: string
  metadata?: Record<string, any>
  jwt_context?: JWTContext
  success: boolean
  error_message?: string
}

export class AuditLogger {
  private static instance: AuditLogger
  
  private constructor() {}
  
  public static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger()
    }
    return AuditLogger.instance
  }

  async log(entry: AuditLogEntry): Promise<void> {
    try {
      const supabase = await createClient()
      
      // Enhanced metadata with JWT context
      const enhancedMetadata = {
        ...entry.metadata,
        jwt_context: entry.jwt_context ? {
          // Only log non-sensitive JWT metadata for security analysis
          expires_at: entry.jwt_context.expires_at,
          token_type: entry.jwt_context.token_type,
          aal: entry.jwt_context.aal, // Authenticator Assurance Level
          amr: entry.jwt_context.amr, // Authentication Methods Reference
          session_id: entry.jwt_context.session_id,
          is_anonymous: entry.jwt_context.is_anonymous,
          token_expires_in: entry.jwt_context.token_expires_in,
          refresh_token_expires_in: entry.jwt_context.refresh_token_expires_in,
          // Calculate time until token expiration for security analysis
          time_until_expiry: entry.jwt_context.expires_at ?
            Math.max(0, entry.jwt_context.expires_at - Math.floor(Date.now() / 1000)) : null,
          // Flag for enhanced security analysis
          has_mfa: entry.jwt_context.aal === 'aal2',
          auth_methods_count: entry.jwt_context.amr?.length || 0
        } : null
      }

      const logEntry = {
        event_type: entry.event_type,
        user_id: entry.user_id || null,
        email: entry.email || null,
        ip_address: entry.ip_address || null,
        user_agent: entry.user_agent || null,
        jwt_context: entry.jwt_context || null,
        metadata: entry.metadata || null,
        success: entry.success,
        error_message: entry.error_message || null
      }

      const { error } = await supabase
        .from('password_reset_audit_logs')
        .insert([logEntry])

      if (error) {
        console.error('Failed to write audit log:', error)
        // Don't throw error to avoid breaking the main flow
      }
    } catch (error) {
      console.error('Audit logging error:', error)
      // Don't throw error to avoid breaking the main flow
    }
  }

  async logPasswordResetRequest(data: {
    email: string
    ip_address?: string
    user_agent?: string
    user_id?: string
    success: boolean
    error_message?: string
  }): Promise<void> {
    await this.log({
      event_type: 'password_reset_requested',
      user_id: data.user_id,
      email: data.email,
      ip_address: data.ip_address,
      user_agent: data.user_agent,
      success: data.success,
      error_message: data.error_message,
      metadata: {
        timestamp: new Date().toISOString(),
      }
    })
  }

  async logPasswordResetCompleted(data: {
    user_id: string
    email: string
    ip_address?: string
    user_agent?: string
    success: boolean
    error_message?: string
  }): Promise<void> {
    await this.log({
      event_type: 'password_reset_completed',
      user_id: data.user_id,
      email: data.email,
      ip_address: data.ip_address,
      user_agent: data.user_agent,
      success: data.success,
      error_message: data.error_message,
      metadata: {
        timestamp: new Date().toISOString(),
      }
    })
  }

  async logRateLimitExceeded(data: {
    email?: string
    ip_address?: string
    user_agent?: string
    limit_type: 'ip' | 'email' | 'global'
    reset_time?: number
  }): Promise<void> {
    await this.log({
      event_type: 'rate_limit_exceeded',
      email: data.email,
      ip_address: data.ip_address,
      user_agent: data.user_agent,
      success: false,
      error_message: `Rate limit exceeded for ${data.limit_type}`,
      metadata: {
        limit_type: data.limit_type,
        reset_time: data.reset_time,
        timestamp: new Date().toISOString(),
      }
    })
  }

  async logInvalidResetAttempt(data: {
    email?: string
    ip_address?: string
    user_agent?: string
    reason: string
    token_hash?: string
  }): Promise<void> {
    await this.log({
      event_type: 'invalid_reset_attempt',
      email: data.email,
      ip_address: data.ip_address,
      user_agent: data.user_agent,
      success: false,
      error_message: data.reason,
      metadata: {
        reason: data.reason,
        token_hash: data.token_hash ? 'present' : 'missing', // Don't log actual token
        timestamp: new Date().toISOString(),
      }
    })
  }

  // Method to get recent audit logs for monitoring (admin only)
  async getRecentLogs(limit: number = 100, event_type?: string): Promise<any[]> {
    try {
      const supabase = await createClient()
      
      let query = supabase
        .from('password_reset_audit_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit)

      if (event_type) {
        query = query.eq('event_type', event_type)
      }

      const { data, error } = await query

      if (error) {
        console.error('Failed to fetch audit logs:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error fetching audit logs:', error)
      return []
    }
  }

  // Method to get suspicious activity (multiple failed attempts)
  async getSuspiciousActivity(timeWindowHours: number = 24): Promise<any[]> {
    try {
      const supabase = await createClient()
      const since = new Date(Date.now() - timeWindowHours * 60 * 60 * 1000).toISOString()

      const { data, error } = await supabase
        .from('password_reset_audit_logs')
        .select('ip_address, email, event_type, created_at, error_message')
        .gte('created_at', since)
        .in('event_type', ['rate_limit_exceeded', 'invalid_reset_attempt', 'password_reset_failed'])
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Failed to fetch suspicious activity:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error fetching suspicious activity:', error)
      return []
    }
  }
}

// Export singleton instance
export const auditLogger = AuditLogger.getInstance()

// SQL for creating the audit log table (to be run in Supabase SQL editor)
export const createAuditLogTableSQL = `
-- Create password reset audit logs table
CREATE TABLE IF NOT EXISTS password_reset_audit_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  event_type TEXT NOT NULL CHECK (event_type IN ('password_reset_requested', 'password_reset_completed', 'password_reset_failed', 'rate_limit_exceeded', 'invalid_reset_attempt')),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  email TEXT,
  ip_address INET,
  user_agent TEXT,
  metadata JSONB,
  success BOOLEAN NOT NULL DEFAULT false,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_password_reset_audit_logs_created_at ON password_reset_audit_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_password_reset_audit_logs_event_type ON password_reset_audit_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_password_reset_audit_logs_email ON password_reset_audit_logs(email);
CREATE INDEX IF NOT EXISTS idx_password_reset_audit_logs_ip_address ON password_reset_audit_logs(ip_address);
CREATE INDEX IF NOT EXISTS idx_password_reset_audit_logs_user_id ON password_reset_audit_logs(user_id);

-- Enable Row Level Security
ALTER TABLE password_reset_audit_logs ENABLE ROW LEVEL SECURITY;

-- Create policy for admin access only
CREATE POLICY "Admin can view all audit logs" ON password_reset_audit_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Create policy for system inserts (no user context needed)
CREATE POLICY "System can insert audit logs" ON password_reset_audit_logs
  FOR INSERT WITH CHECK (true);

-- Grant necessary permissions
GRANT SELECT, INSERT ON password_reset_audit_logs TO authenticated;
GRANT SELECT, INSERT ON password_reset_audit_logs TO anon;
`;
