import Stripe from 'stripe'

let stripeInstance: Stripe | null = null

function getStripe(): Stripe {
  if (!stripeInstance) {
    if (!process.env.STRIPE_SECRET_KEY) {
      throw new Error('STRIPE_SECRET_KEY env var is missing')
    }
    
    stripeInstance = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2023-10-16',
    })
  }
  
  return stripeInstance
}

// Export a getter that initializes lazily at runtime
export const stripe = new Proxy({} as Stripe, {
  get(target, prop) {
    const stripeInstance = getStripe()
    const value = stripeInstance[prop as keyof Stripe]
    return typeof value === 'function' ? value.bind(stripeInstance) : value
  }
}) 