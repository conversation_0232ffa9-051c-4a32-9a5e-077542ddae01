'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useAuth } from '@/hooks/useAuth'

interface LogoutButtonProps {
  variant?: 'default' | 'ghost' | 'outline'
  className?: string
  children?: React.ReactNode
}

export function LogoutButton({ variant = 'ghost', className, children }: LogoutButtonProps) {
  const { signOut } = useAuth()

  const handleLogout = async () => {
    try {
      await signOut()
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Logout error:', error)
      }
    }
  }

  return (
    <Button 
      onClick={handleLogout}
      variant={variant} 
      className={className}
    >
      {children || 'Cerrar Sesión'}
    </Button>
  )
} 