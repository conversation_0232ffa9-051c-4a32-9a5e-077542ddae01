# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# next.js
/.next/
/out/

# production
/build
/dist/

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# SECURITY - Environment Variables & API Keys
# ===========================================
# Environment files (NEVER commit these)
.env
.env.local
.env.production
.env.development
.env.staging
.env.test
.env.*.local

# Backup env files
.env.backup
.env.save
.env.old
.env.bak

# IDE and editor specific env files
.vscode/settings.json
.idea/workspace.xml

# SECURITY - Supabase
# ===================
# Supabase CLI files
supabase/.env
supabase/.env.local
supabase/.env.example
supabase/config.toml.local
supabase/seed.sql.backup

# SECURITY - Stripe
# =================
# Stripe CLI files
.stripecli/
stripe-cli.log

# SECURITY - Google APIs
# ======================
# Google service account keys
google-credentials.json
service-account-key.json
google-service-account.json
*-service-account-key.json
credentials.json
client_secret.json

# SECURITY - General Sensitive Files
# ==================================
# SSL certificates and keys
*.crt
*.key
*.p12
*.pfx
*.p8
*.pem
*.csr

# Database files
*.db
*.sqlite
*.sqlite3
database.json

# Configuration files with sensitive data
config.json
secrets.json
private.json
auth.json

# Log files that might contain sensitive data
*.log
logs/
*.log.*

# Temporary and cache files
tmp/
temp/
.cache/
.temp/

# SECURITY - Development Tools
# ============================
# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor backup files
*~
*.swp
*.swo
*#
.#*

# Package manager
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.yarn-integrity

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# SECURITY - Deployment & Infrastructure
# ======================================
# Vercel
.vercel
.vercel.json

# Docker
docker-compose.override.yml
.dockerignore

# Terraform (if used for infrastructure)
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# SECURITY - Build & Distribution
# ===============================
# TypeScript
*.tsbuildinfo
next-env.d.ts

# Build outputs
/build
/dist
*.tar.gz
*.zip

# Coverage reports
coverage/
*.lcov
.nyc_output/

# SECURITY - Memory Bank (Keep our documentation safe but exclude sensitive versions)
# ===================================================================================
# Keep memory bank but exclude any sensitive backup versions
memory-bank/*.backup
memory-bank/*.old
memory-bank/sensitive/
memory-bank/private/

# SECURITY - Project Specific
# ===========================
# API documentation with real endpoints
api-docs-production.md
live-endpoints.json

# User data exports
user-exports/
data-exports/
*.csv.backup

# Error dumps and crash reports
crash-reports/
error-dumps/
*.dmp

# SECURITY - Additional Safety Measures
# =====================================
# Files that commonly contain secrets by mistake
TODO-SECRETS.md
NOTES-API-KEYS.txt
passwords.txt
secrets.txt
api-keys.txt
tokens.txt
credentials.txt

# Common names for config files that might have secrets
.secrets
.private
.confidential
.secure

# === Custom ignores requested ===
# Ignore all SQL migration/setup files
*.sql

# Ignore all Markdown documentation files
*.md

# Ignore Cursor workspace metadata
.cursor/

# Ignore Memory Bank documentation directory entirely
memory-bank/
