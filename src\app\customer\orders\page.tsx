'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { OrderTracking } from '@/components/orders/order-tracking'
import { Button } from '@/components/ui/button'
import { createClient } from '@/utils/supabase/client'

interface Order {
  id: string
  status: 'pending' | 'confirmed' | 'in-transit' | 'delivered' | 'cancelled'
  pickup_address: {
    street_address: string
    city: string
    state: string
    postal_code: string
    contact_name: string
    contact_phone: string
  }
  delivery_addresses: {
    street_address: string
    city: string
    state: string
    postal_code: string
    recipient_name: string
    phone: string
  }[]
  package_details: {
    description: string
    weight: string
    dimensions: string
    value: string
    special_instructions?: string
  }
  total_cost: number
  created_at: string
  updated_at: string
}

export default function OrdersPage() {
  const { user, profile, loading } = useAuth()
  const router = useRouter()
  const [orders, setOrders] = useState<Order[]>([])
  const [ordersLoading, setOrdersLoading] = useState(true)

  const supabase = createClient()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/')
      return
    }

    if (!loading && user) {
      fetchOrders()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, loading, router])

  const fetchOrders = async () => {
    if (!user) return
    try {
      const { data, error } = await supabase
        .from('orders')
        .select('*')
        .eq('customer_id', user?.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching orders:', error)
        return
      }

      setOrders(data || [])
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setOrdersLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    )
  }

  if (!user || !profile) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                onClick={() => router.push('/customer/dashboard')}
                className="text-blue-600"
              >
                ← Volver al Panel
              </Button>
              <h1 className="text-xl font-bold text-black">Mis Pedidos</h1>
            </div>
            <Button onClick={() => router.push('/customer/orders/new')}>
              + Nuevo Pedido
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <OrderTracking orders={orders} loading={ordersLoading} />
        </div>
      </main>
    </div>
  )
} 