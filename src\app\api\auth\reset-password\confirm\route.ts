import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'
import { auditLogger } from '@/lib/audit-logger'
import { getClientIP } from '@/lib/rate-limit'
import { passwordValidator, PasswordValidator } from '@/lib/password-validator'
import { customerPasswordResetService } from '@/lib/customer-password-reset'

export async function POST(request: NextRequest) {
  const clientIP = getClientIP(request)
  const userAgent = request.headers.get('user-agent') || 'unknown'
  
  try {
    const { password, confirmPassword, code } = await request.json()

    // Input validation
    if (!code || typeof code !== 'string') {
      await auditLogger.logInvalidResetAttempt({
        ip_address: clientIP,
        user_agent: userAgent,
        reason: 'Missing or invalid reset code',
        token_hash: code
      })
      
      return NextResponse.json(
        { error: 'Invalid or missing reset code' },
        { status: 400 }
      )
    }

    if (!password || !confirmPassword) {
      await auditLogger.logInvalidResetAttempt({
        ip_address: clientIP,
        user_agent: userAgent,
        reason: 'Missing password or confirmation',
        token_hash: code
      })
      
      return NextResponse.json(
        { error: 'Password and confirmation are required' },
        { status: 400 }
      )
    }

    if (password !== confirmPassword) {
      await auditLogger.logInvalidResetAttempt({
        ip_address: clientIP,
        user_agent: userAgent,
        reason: 'Password confirmation mismatch',
        token_hash: code
      })
      
      return NextResponse.json(
        { error: 'Passwords do not match' },
        { status: 400 }
      )
    }

    // Initialize Supabase client for password reset verification
    const supabase = await createClient()
    let userEmail = 'unknown'
    let customerData = null

    // Use verifyOtp for password reset confirmation with PKCE flow support
    // First verify the recovery code and create a session
    const { data: verifyData, error: verifyError } = await supabase.auth.verifyOtp({
      token_hash: code,
      type: 'recovery'
    })

    if (verifyError) {
      console.error('Recovery code verification error:', verifyError)

      await auditLogger.logInvalidResetAttempt({
        ip_address: clientIP,
        user_agent: userAgent,
        reason: `Recovery code verification failed: ${verifyError.message}`,
        token_hash: code
      })

      // Handle specific error types
      if (verifyError.message.includes('Invalid') || verifyError.message.includes('expired')) {
        return NextResponse.json(
          { error: 'Reset link is invalid or has expired. Please request a new password reset.' },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Unable to verify reset code. Please try again or request a new reset link.' },
        { status: 500 }
      )
    }

    if (!verifyData.user || !verifyData.session) {
      await auditLogger.logInvalidResetAttempt({
        ip_address: clientIP,
        user_agent: userAgent,
        reason: 'No user or session data returned after recovery verification',
        token_hash: code
      })

      return NextResponse.json(
        { error: 'Unable to verify reset code. Please try again.' },
        { status: 500 }
      )
    }

    // Now we have verified user data, get customer information for enhanced validation
    userEmail = verifyData.user.email || 'unknown'

    // Extract JWT context for enhanced security validation
    const jwtContext = {
      access_token: verifyData.session.access_token,
      refresh_token: verifyData.session.refresh_token,
      expires_at: verifyData.session.expires_at,
      token_type: verifyData.session.token_type,
      user_id: verifyData.user.id,
      aal: verifyData.user.aal || 'aal1', // Authenticator Assurance Level
      amr: verifyData.user.amr || [], // Authentication Methods Reference
      session_id: verifyData.session.user?.id,
      is_anonymous: verifyData.user.is_anonymous || false
    }

    // Get comprehensive customer data for enhanced password requirements
    try {
      customerData = await customerPasswordResetService.getCustomerResetData(verifyData.user.email)
    } catch (customerError) {
      console.log('Could not get customer data, continuing with basic validation:', customerError)
      customerData = null
    }

    // Use customer-specific password requirements if available
    let validator = passwordValidator
    if (customerData) {
      const customerRequirements = customerPasswordResetService.getCustomerPasswordRequirements(customerData)
      validator = new PasswordValidator(customerRequirements)
    }

    // Validate password strength with personal info
    const personalInfo = customerData ? {
      email: customerData.email,
      name: customerData.full_name || customerData.customer_profile?.preferred_name || 'User',
      phone: customerData.phone
    } : { email: userEmail }

    const passwordValidation = validator.validate(password, personalInfo)
    if (!passwordValidation.valid) {
      await auditLogger.logInvalidResetAttempt({
        email: userEmail !== 'unknown' ? userEmail : undefined,
        ip_address: clientIP,
        user_agent: userAgent,
        reason: `Weak password: ${passwordValidation.errors.join(', ')}`,
        token_hash: code
      })

      return NextResponse.json(
        {
          error: passwordValidation.errors[0],
          allErrors: passwordValidation.errors,
          score: passwordValidation.score,
          enhancedSecurity: customerData ? customerPasswordResetService.shouldApplyEnhancedSecurity(customerData) : false
        },
        { status: 400 }
      )
    }

    // Now update the password using the authenticated session
    const { data, error } = await supabase.auth.updateUser({
      password,
      // Enhanced JWT validation for secure password updates with metadata
      data: {
        password_reset_at: new Date().toISOString(),
        password_reset_ip: clientIP,
        password_reset_user_agent: userAgent,
        password_reset_method: 'recovery_code',
        // Include JWT context for audit trail
        password_reset_aal: verifyData.session.user?.aal || 'aal1',
        password_reset_session_id: verifyData.session.user?.id,
        // Enhanced security for high-value customers
        enhanced_security_applied: customerData ? customerPasswordResetService.shouldApplyEnhancedSecurity(customerData) : false
      }
    })

    if (error) {
      console.error('Password update error:', error)

      // Log the failed attempt using verified user data
      await auditLogger.logPasswordResetCompleted({
        user_id: verifyData.user.id,
        email: verifyData.user.email || 'unknown',
        ip_address: clientIP,
        user_agent: userAgent,
        success: false,
        error_message: error.message
      })

      return NextResponse.json(
        { error: 'Unable to update password. Please try again or request a new reset link.' },
        { status: 500 }
      )
    }

    if (!data.user) {
      await auditLogger.logInvalidResetAttempt({
        email: verifyData.user.email,
        ip_address: clientIP,
        user_agent: userAgent,
        reason: 'No user data returned after password update',
        token_hash: code
      })

      return NextResponse.json(
        { error: 'Unable to update password. Please try again.' },
        { status: 500 }
      )
    }

    // Log successful password reset with customer-specific data and JWT context
    if (customerData) {
      await customerPasswordResetService.logCustomerPasswordReset(customerData, {
        ip_address: clientIP,
        user_agent: userAgent,
        success: true,
        jwt_context: jwtContext
      })
    } else {
      // Fallback logging with JWT context for non-customer users
      await auditLogger.log({
        event_type: 'password_reset_completed',
        user_id: data.user.id,
        email: data.user.email || 'unknown',
        ip_address: clientIP,
        user_agent: userAgent,
        success: true,
        jwt_context: jwtContext,
        metadata: {
          user_role: 'unknown',
          password_reset_method: 'recovery_code'
        }
      })
    }

    // Sign out the user after successful password reset
    // This ensures they need to log in with their new password
    await supabase.auth.signOut()

    return NextResponse.json({
      success: true,
      message: 'Password updated successfully. Please log in with your new password.'
    })

  } catch (error) {
    console.error('Password reset confirmation API error:', error)
    
    await auditLogger.logInvalidResetAttempt({
      ip_address: clientIP,
      user_agent: userAgent,
      reason: 'Server error during password reset confirmation'
    })
    
    return NextResponse.json(
      { error: 'Internal server error. Please try again later.' },
      { status: 500 }
    )
  }
}
