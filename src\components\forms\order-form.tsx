'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'

interface DeliveryAddress {
  id: string
  recipient_name: string
  phone: string
  street_address: string
  city: string
  state: string
  postal_code: string
  notes?: string
}

interface OrderFormData {
  pickup_address: {
    street_address: string
    city: string
    state: string
    postal_code: string
    contact_name: string
    contact_phone: string
  }
  delivery_addresses: DeliveryAddress[]
  package_details: {
    description: string
    weight: string
    dimensions: string
    value: string
    special_instructions?: string
  }
  delivery_type: 'single' | 'multiple'
}

interface OrderFormProps {
  onSubmit: (data: OrderFormData) => void
  onCancel: () => void
  loading?: boolean
}

export function OrderForm({ onSubmit, onCancel, loading }: OrderFormProps) {
  const [formData, setFormData] = useState<OrderFormData>({
    pickup_address: {
      street_address: '',
      city: '',
      state: '',
      postal_code: '',
      contact_name: '',
      contact_phone: ''
    },
    delivery_addresses: [{
      id: '1',
      recipient_name: '',
      phone: '',
      street_address: '',
      city: '',
      state: '',
      postal_code: '',
      notes: ''
    }],
    package_details: {
      description: '',
      weight: '',
      dimensions: '',
      value: '',
      special_instructions: ''
    },
    delivery_type: 'single'
  })

  const addDeliveryAddress = () => {
    const newAddress: DeliveryAddress = {
      id: Date.now().toString(),
      recipient_name: '',
      phone: '',
      street_address: '',
      city: '',
      state: '',
      postal_code: '',
      notes: ''
    }
    setFormData(prev => ({
      ...prev,
      delivery_addresses: [...prev.delivery_addresses, newAddress],
      delivery_type: 'multiple'
    }))
  }

  const removeDeliveryAddress = (id: string) => {
    setFormData(prev => ({
      ...prev,
      delivery_addresses: prev.delivery_addresses.filter(addr => addr.id !== id),
      delivery_type: prev.delivery_addresses.length <= 2 ? 'single' : 'multiple'
    }))
  }

  const updateDeliveryAddress = (id: string, field: keyof DeliveryAddress, value: string) => {
    setFormData(prev => ({
      ...prev,
      delivery_addresses: prev.delivery_addresses.map(addr =>
        addr.id === id ? { ...addr, [field]: value } : addr
      )
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Crear Nuevo Pedido</h1>
          <p className="text-gray-600">Completa los detalles para tu pedido de entrega</p>
        </div>
        <Button variant="ghost" onClick={onCancel}>
          Cancelar
        </Button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Pickup Address */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📍 Dirección de Recogida
            </CardTitle>
            <CardDescription>
              ¿Dónde debemos recoger el paquete?
            </CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="pickup_contact_name">Nombre de Contacto</Label>
              <Input
                id="pickup_contact_name"
                value={formData.pickup_address.contact_name}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  pickup_address: { ...prev.pickup_address, contact_name: e.target.value }
                }))}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="pickup_contact_phone">Teléfono de Contacto</Label>
              <Input
                id="pickup_contact_phone"
                type="tel"
                value={formData.pickup_address.contact_phone}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  pickup_address: { ...prev.pickup_address, contact_phone: e.target.value }
                }))}
                required
              />
            </div>
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="pickup_street">Dirección</Label>
              <Input
                id="pickup_street"
                value={formData.pickup_address.street_address}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  pickup_address: { ...prev.pickup_address, street_address: e.target.value }
                }))}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="pickup_city">Ciudad</Label>
              <Input
                id="pickup_city"
                value={formData.pickup_address.city}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  pickup_address: { ...prev.pickup_address, city: e.target.value }
                }))}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="pickup_state">Estado</Label>
              <Input
                id="pickup_state"
                value={formData.pickup_address.state}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  pickup_address: { ...prev.pickup_address, state: e.target.value }
                }))}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="pickup_postal">Código Postal</Label>
              <Input
                id="pickup_postal"
                value={formData.pickup_address.postal_code}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  pickup_address: { ...prev.pickup_address, postal_code: e.target.value }
                }))}
                required
              />
            </div>
          </CardContent>
        </Card>

        {/* Delivery Addresses */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  🚚 Direcciones de Entrega
                </CardTitle>
                <CardDescription>
                  ¿Dónde debemos entregar el(los) paquete(s)?
                </CardDescription>
              </div>
              <Button
                type="button"
                variant="outline"
                onClick={addDeliveryAddress}
                className="text-sm"
              >
                + Agregar Dirección
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {formData.delivery_addresses.map((address, index) => (
              <div key={address.id} className="border border-gray-200 rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">
                    Entrega #{index + 1}
                  </h4>
                  {formData.delivery_addresses.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={() => removeDeliveryAddress(address.id)}
                      className="text-red-600 text-sm"
                    >
                      Eliminar
                    </Button>
                  )}
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor={`recipient_name_${address.id}`}>Nombre del Destinatario</Label>
                    <Input
                      id={`recipient_name_${address.id}`}
                      value={address.recipient_name}
                      onChange={(e) => updateDeliveryAddress(address.id, 'recipient_name', e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`recipient_phone_${address.id}`}>Teléfono del Destinatario</Label>
                    <Input
                      id={`recipient_phone_${address.id}`}
                      type="tel"
                      value={address.phone}
                      onChange={(e) => updateDeliveryAddress(address.id, 'phone', e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor={`street_${address.id}`}>Dirección</Label>
                    <Input
                      id={`street_${address.id}`}
                      value={address.street_address}
                      onChange={(e) => updateDeliveryAddress(address.id, 'street_address', e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`city_${address.id}`}>Ciudad</Label>
                    <Input
                      id={`city_${address.id}`}
                      value={address.city}
                      onChange={(e) => updateDeliveryAddress(address.id, 'city', e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`state_${address.id}`}>Estado</Label>
                    <Input
                      id={`state_${address.id}`}
                      value={address.state}
                      onChange={(e) => updateDeliveryAddress(address.id, 'state', e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`postal_${address.id}`}>Código Postal</Label>
                    <Input
                      id={`postal_${address.id}`}
                      value={address.postal_code}
                      onChange={(e) => updateDeliveryAddress(address.id, 'postal_code', e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor={`notes_${address.id}`}>Notas de Entrega (Opcional)</Label>
                    <Textarea
                      id={`notes_${address.id}`}
                      value={address.notes || ''}
                      onChange={(e) => updateDeliveryAddress(address.id, 'notes', e.target.value)}
                      placeholder="Instrucciones especiales para la entrega..."
                      rows={2}
                    />
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Package Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📦 Detalles del Paquete
            </CardTitle>
            <CardDescription>
              Cuéntanos sobre lo que estás enviando
            </CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="package_description">Descripción del Paquete</Label>
              <Input
                id="package_description"
                value={formData.package_details.description}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  package_details: { ...prev.package_details, description: e.target.value }
                }))}
                placeholder="ej., Documentos, Electrónicos, Ropa..."
                required
              />
              <p className="text-sm text-gray-500 mt-1">
                ¿Qué hay dentro del paquete?
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="package_weight">Peso (kg)</Label>
              <Input
                id="package_weight"
                type="number"
                step="0.1"
                value={formData.package_details.weight}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  package_details: { ...prev.package_details, weight: e.target.value }
                }))}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="package_dimensions">Dimensiones (L x A x H cm)</Label>
              <Input
                id="package_dimensions"
                value={formData.package_details.dimensions}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  package_details: { ...prev.package_details, dimensions: e.target.value }
                }))}
                placeholder="ej., 30 x 20 x 10"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="package_value">Valor Declarado (MXN)</Label>
              <Input
                id="package_value"
                type="number"
                step="0.01"
                value={formData.package_details.value}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  package_details: { ...prev.package_details, value: e.target.value }
                }))}
                required
              />
            </div>
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="special_instructions">Instrucciones Especiales (Opcional)</Label>
              <Textarea
                id="special_instructions"
                value={formData.package_details.special_instructions || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  package_details: { ...prev.package_details, special_instructions: e.target.value }
                }))}
                placeholder="Cualquier requisito de manejo especial..."
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Submit Actions */}
        <div className="flex justify-end space-x-4 pt-6">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancelar
          </Button>
          <Button type="submit" disabled={loading} className="min-w-[120px]">
            {loading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Creando...
              </div>
            ) : (
              'Crear Pedido'
            )}
          </Button>
        </div>
      </form>
    </div>
  )
} 