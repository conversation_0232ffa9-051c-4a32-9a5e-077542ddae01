'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { PasswordInput } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { PasswordStrengthIndicator } from '@/components/ui/password-strength-indicator'
import { PasswordResetErrorAlert, PasswordResetSuccessAlert } from '@/components/ui/error-alert'

export function ConfirmResetPasswordForm() {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  // Removed useAuth: not needed after refactor
  const searchParams = useSearchParams()

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    // Security check: ensure we have valid reset code
    const code = searchParams.get('code') || searchParams.get('token_hash')

    if (!code) {
      setError('Enlace de restablecimiento inválido o expirado')
      setLoading(false)
      return
    }

    if (password !== confirmPassword) {
      setError('Las contraseñas no coinciden')
      setLoading(false)
      return
    }

    try {
      // Use the new API endpoint with enhanced security
      const response = await fetch('/api/auth/reset-password/confirm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          password,
          confirmPassword,
          code
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        setError(data.error || 'Ocurrió un error inesperado')
      } else {
        setSuccess(true)
      }
    } catch (error) {
      console.error('Password reset error:', error)
      setError('Error de conexión. Por favor verifica tu conexión a internet.')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="space-y-6 bg-gradient-to-br from-blue-900/20 to-green-900/20 backdrop-blur-sm border border-blue-400/30 rounded-lg p-6 shadow-xl w-full max-w-md">
        <PasswordResetSuccessAlert
          message="Tu contraseña ha sido actualizada exitosamente. Ya puedes iniciar sesión con tu nueva contraseña."
        />
        <div className="text-center">
          <Link
            href="/login"
            className="text-green-300 hover:text-blue-300 hover:underline font-medium transition-colors"
          >
            Ir al inicio de sesión
          </Link>
        </div>
      </div>
    )
  }

  // Security check for valid reset code
  const code = searchParams.get('code') || searchParams.get('token_hash')
  
  if (!code) {
    return (
      <div className="space-y-6 bg-gradient-to-br from-blue-900/20 to-green-900/20 backdrop-blur-sm border border-blue-400/30 rounded-lg p-6 shadow-xl w-full max-w-md">
        <PasswordResetErrorAlert
          error="El enlace de restablecimiento es inválido o ha expirado. Por favor solicita un nuevo enlace."
        />
        <div className="text-center">
          <Link
            href="/reset-password"
            className="text-green-300 hover:text-blue-300 hover:underline font-medium transition-colors"
          >
            Solicitar nuevo enlace
          </Link>
        </div>
      </div>
    )
  }

  return (
    <form onSubmit={handlePasswordReset} className="space-y-6 bg-gradient-to-br from-blue-900/20 to-green-900/20 backdrop-blur-sm border border-blue-400/30 rounded-lg p-6 shadow-xl w-full max-w-md">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-white">Nueva contraseña</h2>
        <p className="text-gray-300">
          Ingresa tu nueva contraseña
        </p>
      </div>
      
      {error && (
        <PasswordResetErrorAlert
          error={error}
          onDismiss={() => setError('')}
        />
      )}
      
      <div className="space-y-2">
        <Label htmlFor="password" className="text-white">Nueva contraseña *</Label>
        <PasswordInput
          id="password"
          placeholder="••••••••"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          disabled={loading}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="confirmPassword" className="text-white">Confirmar contraseña *</Label>
        <PasswordInput
          id="confirmPassword"
          placeholder="••••••••"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          required
          disabled={loading}
        />
      </div>

      {/* Password Strength Indicator */}
      {password && (
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
          <PasswordStrengthIndicator
            password={password}
            showRequirements={true}
            className="text-white"
          />
        </div>
      )}

      <Button 
        type="submit" 
        className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 text-white border-0" 
        disabled={loading}
      >
        {loading ? 'Actualizando...' : 'Actualizar contraseña'}
      </Button>

      <div className="text-center">
        <Link 
          href="/login" 
          className="text-sm text-gray-300 hover:text-green-300 hover:underline transition-colors"
        >
          Volver al inicio de sesión
        </Link>
      </div>
    </form>
  )
} 