'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { OrderForm } from '@/components/forms/order-form'
import { createClient } from '@/utils/supabase/client'

interface OrderFormData {
  pickup_address: {
    street_address: string
    city: string
    state: string
    postal_code: string
    contact_name: string
    contact_phone: string
  }
  delivery_addresses: {
    id: string
    recipient_name: string
    phone: string
    street_address: string
    city: string
    state: string
    postal_code: string
    notes?: string
  }[]
  package_details: {
    description: string
    weight: string
    dimensions: string
    value: string
    special_instructions?: string
  }
  delivery_type: 'single' | 'multiple'
}

export default function NewOrderPage() {
  const { user, profile, loading } = useAuth()
  const router = useRouter()
  const [creating, setCreating] = useState(false)

  const supabase = createClient()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
      </div>
    )
  }

  if (!user || !profile) {
    router.push('/')
    return null
  }

  const handleOrderSubmit = async (orderData: OrderFormData) => {
    setCreating(true)
    try {
      // Create order in database
      const { data, error } = await supabase
        .from('orders')
        .insert({
          customer_id: user.id,
          pickup_address: orderData.pickup_address,
          delivery_addresses: orderData.delivery_addresses,
          package_details: orderData.package_details,
          status: 'pending',
          payment_status: 'pending'
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating order:', error)
        alert('Failed to create order. Please try again.')
        return
      }

      // Redirect to order tracking or dashboard
      router.push(`/customer/orders/${data.id}`)
    } catch (error) {
      console.error('Error:', error)
      alert('Failed to create order. Please try again.')
    } finally {
      setCreating(false)
    }
  }

  const handleCancel = () => {
    router.push('/customer/dashboard')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <OrderForm
        onSubmit={handleOrderSubmit}
        onCancel={handleCancel}
        loading={creating}
      />
    </div>
  )
} 