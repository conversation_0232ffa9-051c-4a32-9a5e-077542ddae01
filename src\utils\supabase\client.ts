import { createBrowserClient } from '@supabase/ssr'

export const createClient = () =>
  createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      // July 2025 JWT enhancements
      auth: {
        flowType: 'pkce', // Use PKCE flow for enhanced security
        persistSession: true,
        detectSessionInUrl: true,
        autoRefreshToken: true,
        // Enhanced JWT validation
        storage: undefined, // Use default storage with JWT improvements
      },
      cookies: {
        get(name: string) {
          if (typeof document === 'undefined') return undefined
          return document.cookie
            .split('; ')
            .find((row) => row.startsWith(`${name}=`))
            ?.split('=')[1]
        },
        set(name: string, value: string, options: { maxAge?: number } = {}) {
          if (typeof document === 'undefined') return
          // Enhanced cookie security for JWT tokens
          const secure = process.env.NODE_ENV === 'production' ? '; secure' : ''
          const sameSite = '; samesite=lax'
          const maxAge = options.maxAge || 60 * 60 * 24 * 7 // 7 days default
          document.cookie = `${name}=${value}; path=/; max-age=${maxAge}${sameSite}${secure}; httponly=false`
        },
        remove(name: string) {
          if (typeof document === 'undefined') return
          const secure = process.env.NODE_ENV === 'production' ? '; secure' : ''
          document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; samesite=lax${secure}`
        },
      },
    }
  ) 