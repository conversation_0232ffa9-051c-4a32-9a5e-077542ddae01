import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ToastProvider } from "@/components/ui/toast";
import { MobileNav } from "@/components/layout/mobile-nav";
import { AuthProvider } from "@/contexts/AuthContext";
import { StagewiseToolbar } from "@stagewise/toolbar-next";
import ReactPlugin from "@stagewise-plugins/react";

const inter = Inter({
  subsets: ["latin"],
  display: 'swap',
});

export const metadata: Metadata = {
  title: "Mouvers - Your Trusted Delivery Platform",
  description: "Delivery and logistics management platform for Mexico",
  keywords: ["delivery", "logistics", "Mexico", "mouvers", "shipping"],
  authors: [{ name: "Mouvers Team" }],
  creator: "Mouvers",
  publisher: "Mouvers",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body suppressHydrationWarning className={`${inter.className} antialiased`}>
        {/* Provide ReactToolbar plugin correctly */}
        <StagewiseToolbar config={{ plugins: [ReactPlugin] }} />
        <AuthProvider>
          <ToastProvider>
            {children}
            <MobileNav />
          </ToastProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
