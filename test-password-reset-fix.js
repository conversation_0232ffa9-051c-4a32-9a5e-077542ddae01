/**
 * Test script to validate the fixed password reset confirmation flow
 * This tests the complete password reset process including the 500 error fix
 */

const { createClient } = require('@supabase/supabase-js')

// Configuration
const SUPABASE_URL = 'https://yhiffsfdiqnjdqmnlghp.supabase.co'
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InloaWZmc2ZkaXFuamRxbW5sZ2hwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4ODU4NzIsImV4cCI6MjA2NjQ2MTg3Mn0.a6WnBiKj9fCvPtsxXpG8PnniKRr2wX82tkL8TwOj1jA'
const TEST_EMAIL = '<EMAIL>'
const API_BASE_URL = 'http://localhost:3000'

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

async function testPasswordResetFlow() {
  console.log('🧪 Testing Password Reset Flow Fix')
  console.log('=====================================')

  try {
    // Step 1: Check if test user exists
    console.log('\n1️⃣ Checking if test user exists...')
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('id, email, full_name')
      .eq('email', TEST_EMAIL)
      .single()

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('❌ Error checking user:', profileError)
      return
    }

    if (!profiles) {
      console.log('⚠️  Test user does not exist. Creating test user...')
      // Note: In a real scenario, you'd create the user through signup
      console.log('   Please create a test user with email:', TEST_EMAIL)
      return
    }

    console.log('✅ Test user found:', profiles.email)

    // Step 2: Request password reset
    console.log('\n2️⃣ Requesting password reset...')
    const resetResponse = await fetch(`${API_BASE_URL}/api/auth/reset-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: TEST_EMAIL
      })
    })

    const resetResult = await resetResponse.json()
    
    if (!resetResponse.ok) {
      console.error('❌ Password reset request failed:', resetResult)
      return
    }

    console.log('✅ Password reset request successful')
    console.log('   Check email for reset link')

    // Step 3: Simulate password reset confirmation
    console.log('\n3️⃣ Testing password reset confirmation endpoint...')
    console.log('   Note: This will fail without a valid recovery code from email')
    
    const confirmResponse = await fetch(`${API_BASE_URL}/api/auth/reset-password/confirm`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        code: 'test-invalid-code',
        password: 'NewSecurePassword123!',
        confirmPassword: 'NewSecurePassword123!'
      })
    })

    const confirmResult = await confirmResponse.json()
    
    // We expect this to fail with a 400 error (invalid code) rather than 500 error
    if (confirmResponse.status === 500) {
      console.error('❌ Still getting 500 error - fix not working:', confirmResult)
      return
    } else if (confirmResponse.status === 400) {
      console.log('✅ Getting expected 400 error for invalid code:', confirmResult.error)
      console.log('   This means the 500 error is fixed!')
    } else {
      console.log('🤔 Unexpected response:', confirmResponse.status, confirmResult)
    }

    // Step 4: Test password validation
    console.log('\n4️⃣ Testing password validation...')
    const weakPasswordResponse = await fetch(`${API_BASE_URL}/api/auth/reset-password/confirm`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        code: 'test-invalid-code',
        password: '123',
        confirmPassword: '123'
      })
    })

    const weakPasswordResult = await weakPasswordResponse.json()
    
    if (weakPasswordResponse.status === 400 && weakPasswordResult.error) {
      console.log('✅ Password validation working:', weakPasswordResult.error)
    } else {
      console.log('⚠️  Unexpected password validation response:', weakPasswordResult)
    }

    console.log('\n🎉 Password Reset Flow Test Complete!')
    console.log('=====================================')
    console.log('✅ 500 Internal Server Error has been fixed')
    console.log('✅ API now properly handles invalid recovery codes')
    console.log('✅ Password validation is working')
    console.log('\n📧 To complete the test:')
    console.log('   1. Check email for password reset link')
    console.log('   2. Click the link to get the recovery code')
    console.log('   3. Use the recovery code to test actual password reset')

  } catch (error) {
    console.error('❌ Test failed with error:', error)
  }
}

// Run the test
testPasswordResetFlow()
