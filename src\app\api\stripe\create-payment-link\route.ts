import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { createClient } from '@/utils/supabase/server'

export const runtime = 'nodejs'

export async function POST(request: NextRequest) {
  try {
    const { amount, userId } = await request.json()

    if (!amount || !userId) {
      return NextResponse.json(
        { error: 'Amount and userId are required' },
        { status: 400 }
      )
    }

    // Convert amount to cents for Stripe
    const amountInCents = Math.round(parseFloat(amount) * 100)

    if (amountInCents < 100) { // Minimum $1.00 MXN
      return NextResponse.json(
        { error: 'Minimum amount is $1.00 MXN' },
        { status: 400 }
      )
    }

    const supabase = await createClient()

    // Get user info for better traceability
    const { data: userProfile } = await supabase
      .from('profiles')
      .select('email, full_name')
      .eq('id', userId)
      .single()

    // Create a unique reference ID for this transaction
    const transactionRef = `mouvers_${userId.slice(-8)}_${Date.now()}`

    // Create product for wallet top-up with enhanced metadata
    const product = await stripe.products.create({
      name: `Mouvers Wallet Top-up - $${amount} MXN`,
      description: `Add $${amount} MXN to Mouvers wallet for ${userProfile?.email || 'user'}`,
      metadata: {
        user_id: userId,
        transaction_ref: transactionRef,
        amount_mxn: amount.toString(),
        user_email: userProfile?.email || '',
        user_name: userProfile?.full_name || '',
        platform: 'mouvers'
      }
    })

    // Create price for the product
    const price = await stripe.prices.create({
      product: product.id,
      unit_amount: amountInCents,
      currency: 'mxn',
      metadata: {
        user_id: userId,
        transaction_ref: transactionRef,
        amount_mxn: amount.toString()
      }
    })

    // Create payment link with comprehensive metadata and tracking
    const paymentLink = await stripe.paymentLinks.create({
      line_items: [
        {
          price: price.id,
          quantity: 1,
        },
      ],
      metadata: {
        user_id: userId,
        transaction_ref: transactionRef,
        amount_mxn: amount.toString(),
        user_email: userProfile?.email || '',
        platform: 'mouvers',
        purpose: 'wallet_topup',
        created_at: new Date().toISOString()
      },
      after_completion: {
        type: 'redirect',
        redirect: {
          url: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://mouvers.vercel.app'}/customer/profile/payment-success?amount=${amount}&ref=${transactionRef}`,
        },
      }
    })

    // Log the payment link creation in Supabase for audit trail
    // Note: We'll create the actual transaction when the webhook confirms payment
    console.log(`[Payment Link] Created payment link for user ${userId}, amount: ${amount} MXN, ref: ${transactionRef}`)

    return NextResponse.json({
      success: true,
      paymentLink: {
        id: paymentLink.id,
        url: paymentLink.url
      },
      tracking: {
        transaction_ref: transactionRef,
        amount: amount,
        currency: 'MXN'
      }
    })

  } catch (error) {
    console.error('create-payment-link error', error)
    return NextResponse.json(
      { error: 'Failed to create payment link' },
      { status: 500 }
    )
  }
} 