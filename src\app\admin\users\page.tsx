'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { ProtectedRoute } from '@/components/auth/protected-route'
import { UsersManagement } from '@/components/admin/users-management'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { createClient } from '@/utils/supabase/client'

interface User {
  id: string
  email: string
  full_name: string
  phone?: string
  role: 'customer' | 'admin'
  created_at: string
  updated_at: string
  orders_count?: number
  total_spent?: number
  last_order?: string
}

function AdminUsersContent() {
  const { user, isAdmin } = useAuth()
  const [users, setUsers] = useState<User[]>([])
  const [usersLoading, setUsersLoading] = useState(true)

  const supabase = createClient()

  useEffect(() => {
    if (user && isAdmin) {
      fetchUsers()
    }
  }, [user, isAdmin])

  const fetchUsers = async () => {
    try {
      setUsersLoading(true)
      
      // Optimized query: Get all users and their order statistics in fewer queries
      const { data: usersData, error: usersError } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })

      if (usersError) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error fetching users:', usersError)
        }
        return
      }

      // Get order statistics for all customers in one query
      const customerIds = usersData
        .filter(user => user.role === 'customer')
        .map(user => user.id)

      const orderStats: Record<string, {
        orders_count: number
        total_spent: number
        last_order?: string
      }> = {}

      if (customerIds.length > 0) {
        const { data: orders, error: ordersError } = await supabase
          .from('orders')
          .select('customer_id, total_cost, created_at')
          .in('customer_id', customerIds)

        if (!ordersError && orders) {
          // Process order statistics
          orders.forEach(order => {
            const customerId = order.customer_id
            if (!orderStats[customerId]) {
              orderStats[customerId] = {
                orders_count: 0,
                total_spent: 0
              }
            }
            
            orderStats[customerId].orders_count++
            orderStats[customerId].total_spent += order.total_cost || 0
            
            // Track latest order
            if (!orderStats[customerId].last_order || 
                new Date(order.created_at) > new Date(orderStats[customerId].last_order!)) {
              orderStats[customerId].last_order = order.created_at
            }
          })
        }
      }

      // Combine user data with order statistics
      const usersWithStats = usersData.map(userData => ({
        ...userData,
        ...orderStats[userData.id]
      }))

      setUsers(usersWithStats)
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error:', error)
      }
    } finally {
      setUsersLoading(false)
    }
  }

    const handleCreateUser = async () => {
    try {
      // Note: In a real implementation, you would create the user through Supabase Auth
      // and then the profile would be created automatically via the trigger
      
      alert('User creation would be implemented through Supabase Auth API')
      
      // For now, just refresh the users list
      await fetchUsers()
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error creating user:', error)
      }
      alert('Failed to create user')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                onClick={() => window.history.back()}
                className="text-blue-600"
              >
                ← Volver al Panel
              </Button>
              <h1 className="text-xl font-bold text-black">Gestión de Usuarios</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                {user?.email || 'Admin'}
              </span>
              <Badge variant="default">Admin</Badge>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <UsersManagement 
            users={users} 
            onCreateUser={handleCreateUser}
            loading={usersLoading} 
          />
        </div>
      </main>
    </div>
  )
}

export default function AdminUsersPage() {
  return (
    <ProtectedRoute requireAdmin>
      <AdminUsersContent />
    </ProtectedRoute>
  )
} 